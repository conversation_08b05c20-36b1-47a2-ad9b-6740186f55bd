import { Tool, ToolR<PERSON><PERSON>, AgentContext, ToolCall } from '../types';
export declare class ToolRegistry {
    private static instance;
    private tools;
    private logger;
    private executionQueue;
    private toolStats;
    private statsFile;
    private constructor();
    static getInstance(): ToolRegistry;
    private loadToolStats;
    private saveToolStats;
    private updateToolStats;
    private registerDefaultTools;
    registerTool(tool: Tool): void;
    unregisterTool(toolName: string): boolean;
    getTool(toolName: string): Tool | undefined;
    getAllTools(): Tool[];
    getToolNames(): string[];
    hasToolCalling(): boolean;
    executeTool(toolCall: ToolCall, context: AgentContext): Promise<ToolResult>;
    executeToolsParallel(toolCalls: ToolCall[], context: AgentContext, maxParallel?: number): Promise<ToolResult[]>;
    executeToolsSequential(toolCalls: ToolCall[], context: AgentContext): Promise<ToolResult[]>;
    private isCriticalTool;
    getToolSchema(toolName: string): any;
    getAllToolSchemas(): any[];
    validateToolCall(toolCall: ToolCall): boolean;
    getToolUsageStats(): Record<string, {
        calls: number;
        successes: number;
        failures: number;
        averageDuration: number;
    }>;
    executeToolWithRetry(toolCall: ToolCall, context: AgentContext, maxRetries?: number): Promise<ToolResult>;
    analyzeDependencies(toolCalls: ToolCall[]): ToolCall[][];
    private createBatches;
    executeToolChain(toolCalls: ToolCall[], context: AgentContext, options?: {
        stopOnFailure?: boolean;
        maxParallel?: number;
        enableRetry?: boolean;
        enableLearning?: boolean;
        adaptiveExecution?: boolean;
    }): Promise<ToolResult[]>;
    private optimizeExecutionOrder;
    private updateSuccessPatterns;
    private updateFailurePatterns;
    private analyzeExecutionPatterns;
    executeAutonomousWorkflow(workflow: {
        name: string;
        description: string;
        steps: ToolCall[];
        conditions?: {
            continueOnFailure?: boolean;
            maxRetries?: number;
            timeout?: number;
        };
    }, context: AgentContext): Promise<{
        success: boolean;
        results: ToolResult[];
        executionTime: number;
        stepsCompleted: number;
    }>;
}
//# sourceMappingURL=ToolRegistry.d.ts.map