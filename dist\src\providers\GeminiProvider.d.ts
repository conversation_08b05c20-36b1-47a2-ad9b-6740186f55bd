import { LL<PERSON>rovider, LLMOptions, LLMResponse, <PERSON><PERSON><PERSON>all, <PERSON>l<PERSON><PERSON>ult, AgentContext } from '../../types';
export declare class GeminiProvider implements LLMProvider {
    readonly name = "gemini";
    private client;
    private logger;
    private toolRegistry;
    constructor(config: Record<string, unknown>);
    generateResponse(prompt: string, options?: LLMOptions): Promise<LLMResponse>;
    generateStreamResponse(prompt: string, options?: LLMOptions): AsyncGenerator<string, void, unknown>;
    supportsToolCalling(): boolean;
    callTool(toolCall: ToolCall, context: AgentContext): Promise<ToolResult>;
    validateApiKey(): Promise<boolean>;
    getAvailableModels(): Promise<string[]>;
    getDefaultModel(): string;
    getMaxTokens(model?: string): number;
    estimateTokens(text: string): number;
    calculateCost(usage: {
        promptTokens: number;
        completionTokens: number;
    }, model?: string): number;
    private buildToolDescriptions;
    private parseToolCalls;
}
//# sourceMappingURL=GeminiProvider.d.ts.map