"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectIndexer = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
const fast_glob_1 = __importDefault(require("fast-glob"));
const simple_git_1 = __importDefault(require("simple-git"));
const toml_1 = __importDefault(require("toml"));
const mime_types_1 = __importDefault(require("mime-types"));
const Logger_1 = require("../utils/Logger");
const ConfigManager_1 = require("../config/ConfigManager");
class ProjectIndexer {
    static instance;
    logger;
    configManager;
    constructor() {
        this.logger = Logger_1.Logger.getInstance();
        this.configManager = ConfigManager_1.ConfigManager.getInstance();
    }
    static getInstance() {
        if (!ProjectIndexer.instance) {
            ProjectIndexer.instance = new ProjectIndexer();
        }
        return ProjectIndexer.instance;
    }
    async indexProject(rootPath) {
        const startTime = Date.now();
        try {
            this.logger.info(`Starting project indexing: ${rootPath}`);
            const [projectType, files, gitInfo, packageInfo] = await Promise.all([
                this.detectProjectType(rootPath),
                this.indexFiles(rootPath),
                this.getGitInfo(rootPath),
                this.getPackageInfo(rootPath)
            ]);
            const dependencies = this.extractDependencies(packageInfo);
            const context = {
                rootPath,
                projectType,
                dependencies,
                files,
                gitInfo,
                packageInfo
            };
            const duration = Date.now() - startTime;
            this.logger.info(`Project indexing completed`, {
                rootPath,
                projectType,
                fileCount: files.length,
                dependencyCount: Object.keys(dependencies).length,
                duration
            });
            return context;
        }
        catch (error) {
            this.logger.error('Project indexing failed', {
                rootPath,
                error: error.message
            });
            throw error;
        }
    }
    async detectProjectType(rootPath) {
        const indicators = [
            { file: 'package.json', type: 'nodejs' },
            { file: 'requirements.txt', type: 'python' },
            { file: 'pyproject.toml', type: 'python' },
            { file: 'setup.py', type: 'python' },
            { file: 'Pipfile', type: 'python' },
            { file: 'Cargo.toml', type: 'rust' },
            { file: 'go.mod', type: 'go' },
            { file: 'pom.xml', type: 'java' },
            { file: 'build.gradle', type: 'java' },
            { file: 'build.gradle.kts', type: 'java' },
            { file: 'composer.json', type: 'php' },
            { file: 'Gemfile', type: 'ruby' },
            { file: 'CMakeLists.txt', type: 'cpp' },
            { file: 'Makefile', type: 'cpp' },
            { file: 'pubspec.yaml', type: 'dart' },
            { file: 'mix.exs', type: 'elixir' },
            { file: 'deno.json', type: 'deno' },
            { file: 'bun.lockb', type: 'bun' },
            { file: 'Podfile', type: 'ios' },
            { file: 'AndroidManifest.xml', type: 'android' },
            { file: 'flutter.yaml', type: 'flutter' }
        ];
        // Check for exact file matches first
        for (const indicator of indicators) {
            const filePath = path_1.default.join(rootPath, indicator.file);
            if (await fs_extra_1.default.pathExists(filePath)) {
                return indicator.type;
            }
        }
        // Check for pattern-based files
        try {
            const files = await fs_extra_1.default.readdir(rootPath);
            // Check for .csproj, .sln files
            if (files.some(file => file.endsWith('.csproj') || file.endsWith('.sln'))) {
                return 'csharp';
            }
            // Check for .xcodeproj directories
            if (files.some(file => file.endsWith('.xcodeproj'))) {
                return 'ios';
            }
            // Count file extensions to determine primary language
            const extCounts = {};
            for (const file of files) {
                const ext = path_1.default.extname(file).toLowerCase();
                if (ext) {
                    extCounts[ext] = (extCounts[ext] || 0) + 1;
                }
            }
            // Sort by count and determine type
            const sortedExts = Object.entries(extCounts).sort(([, a], [, b]) => b - a);
            if (sortedExts.length > 0) {
                const [mostCommonExt, count] = sortedExts[0];
                // Only consider if there are enough files of this type
                if (count >= 2) {
                    switch (mostCommonExt) {
                        case '.py':
                            return 'python';
                        case '.js':
                        case '.ts':
                        case '.jsx':
                        case '.tsx':
                            return 'nodejs';
                        case '.rs':
                            return 'rust';
                        case '.go':
                            return 'go';
                        case '.java':
                        case '.kt':
                            return 'java';
                        case '.php':
                            return 'php';
                        case '.rb':
                            return 'ruby';
                        case '.cs':
                            return 'csharp';
                        case '.cpp':
                        case '.cc':
                        case '.cxx':
                        case '.c':
                        case '.h':
                        case '.hpp':
                            return 'cpp';
                        case '.swift':
                            return 'swift';
                        case '.dart':
                            return 'dart';
                        case '.ex':
                        case '.exs':
                            return 'elixir';
                        case '.html':
                        case '.css':
                            return 'web';
                        case '.vue':
                            return 'vue';
                        case '.svelte':
                            return 'svelte';
                        default:
                            break;
                    }
                }
            }
        }
        catch (error) {
            this.logger.debug('Failed to analyze file extensions', {
                rootPath,
                error: error.message
            });
        }
        return 'unknown';
    }
    async indexFiles(rootPath) {
        const config = this.configManager.getConfig().context;
        const files = [];
        try {
            const globPatterns = config.includePatterns.length > 0
                ? config.includePatterns
                : ['**/*'];
            const filePaths = await (0, fast_glob_1.default)(globPatterns, {
                cwd: rootPath,
                ignore: config.excludePatterns,
                onlyFiles: false,
                absolute: true,
                stats: true
            });
            for (const entry of filePaths) {
                try {
                    const filePath = typeof entry === 'string' ? entry : entry.path;
                    const stats = typeof entry === 'string'
                        ? await fs_extra_1.default.stat(filePath)
                        : entry.stats;
                    // Skip files that are too large
                    if (stats.isFile() && stats.size > config.maxFileSize) {
                        continue;
                    }
                    const fileInfo = {
                        path: path_1.default.relative(rootPath, filePath),
                        type: stats.isDirectory() ? 'directory' : 'file',
                        size: stats.size,
                        lastModified: stats.mtime,
                        permissions: stats.mode.toString(8)
                    };
                    // Read content for small text files
                    if (stats.isFile() &&
                        stats.size <= config.maxFileSize &&
                        this.isTextFile(filePath)) {
                        try {
                            fileInfo.content = await fs_extra_1.default.readFile(filePath, 'utf8');
                        }
                        catch (error) {
                            // Skip files that can't be read as text
                        }
                    }
                    files.push(fileInfo);
                }
                catch (error) {
                    this.logger.debug('Failed to index file', {
                        file: typeof entry === 'string' ? entry : entry.path,
                        error: error.message
                    });
                }
            }
        }
        catch (error) {
            this.logger.error('Failed to index files', {
                rootPath,
                error: error.message
            });
        }
        return files;
    }
    isTextFile(filePath) {
        const ext = path_1.default.extname(filePath).toLowerCase();
        // Known text extensions
        const textExtensions = [
            '.txt', '.md', '.json', '.yaml', '.yml', '.xml', '.html', '.css',
            '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.c', '.cpp', '.h',
            '.rs', '.go', '.php', '.rb', '.cs', '.sh', '.bat', '.ps1',
            '.sql', '.r', '.scala', '.kt', '.swift', '.dart', '.lua',
            '.vim', '.ini', '.cfg', '.conf', '.log', '.csv', '.toml',
            '.dockerfile', '.gitignore', '.gitattributes', '.editorconfig',
            '.env', '.example', '.sample', '.template'
        ];
        if (textExtensions.includes(ext)) {
            return true;
        }
        // Use mime-types to check if it's a text file
        const mimeType = mime_types_1.default.lookup(filePath);
        if (mimeType && mimeType.startsWith('text/')) {
            return true;
        }
        // Check for files without extensions that are typically text
        const basename = path_1.default.basename(filePath).toLowerCase();
        const textFiles = [
            'readme', 'license', 'changelog', 'makefile', 'dockerfile',
            'jenkinsfile', 'vagrantfile', 'gemfile', 'rakefile'
        ];
        return textFiles.includes(basename);
    }
    async getGitInfo(rootPath) {
        try {
            const gitDir = path_1.default.join(rootPath, '.git');
            if (!await fs_extra_1.default.pathExists(gitDir)) {
                return undefined;
            }
            const git = (0, simple_git_1.default)(rootPath);
            // Get current branch
            const branch = await git.revparse(['--abbrev-ref', 'HEAD']);
            // Get remotes
            const remotes = await git.getRemotes(true);
            const remoteUrls = remotes.map(remote => remote.refs.fetch || remote.refs.push);
            // Get status
            const status = await git.status();
            const statusText = status.files.length > 0 ? 'dirty' : 'clean';
            // Get last commit
            const log = await git.log({ maxCount: 1 });
            const lastCommit = log.latest ?
                `${log.latest.hash.substring(0, 7)} - ${log.latest.message}` : '';
            const gitInfo = {
                branch: branch.trim(),
                remotes: remoteUrls,
                status: statusText,
                lastCommit
            };
            this.logger.debug('Git info retrieved', {
                rootPath,
                branch: gitInfo.branch,
                remoteCount: gitInfo.remotes.length,
                status: gitInfo.status
            });
            return gitInfo;
        }
        catch (error) {
            this.logger.debug('Failed to get git info', {
                rootPath,
                error: error.message
            });
            return undefined;
        }
    }
    async getPackageInfo(rootPath) {
        const packageFiles = [
            { file: 'package.json', parser: this.parsePackageJson },
            { file: 'requirements.txt', parser: this.parseRequirementsTxt },
            { file: 'Cargo.toml', parser: this.parseCargoToml },
            { file: 'go.mod', parser: this.parseGoMod },
            { file: 'pom.xml', parser: this.parsePomXml }
        ];
        for (const { file, parser } of packageFiles) {
            const filePath = path_1.default.join(rootPath, file);
            if (await fs_extra_1.default.pathExists(filePath)) {
                try {
                    return await parser.call(this, filePath);
                }
                catch (error) {
                    this.logger.debug(`Failed to parse ${file}`, {
                        filePath,
                        error: error.message
                    });
                }
            }
        }
        return undefined;
    }
    async parsePackageJson(filePath) {
        const content = await fs_extra_1.default.readJson(filePath);
        return {
            name: content.name || 'unknown',
            version: content.version || '0.0.0',
            dependencies: content.dependencies || {},
            devDependencies: content.devDependencies || {},
            scripts: content.scripts || {}
        };
    }
    async parseRequirementsTxt(filePath) {
        const content = await fs_extra_1.default.readFile(filePath, 'utf8');
        const dependencies = {};
        for (const line of content.split('\n')) {
            const trimmed = line.trim();
            if (trimmed && !trimmed.startsWith('#')) {
                const match = trimmed.match(/^([^=<>!]+)([=<>!].+)?$/);
                if (match) {
                    dependencies[match[1].trim()] = match[2]?.trim() || '*';
                }
            }
        }
        return {
            name: path_1.default.basename(path_1.default.dirname(filePath)),
            version: '0.0.0',
            dependencies,
            devDependencies: {},
            scripts: {}
        };
    }
    async parseCargoToml(filePath) {
        try {
            const content = await fs_extra_1.default.readFile(filePath, 'utf8');
            const parsed = toml_1.default.parse(content);
            const dependencies = {};
            const devDependencies = {};
            // Parse dependencies
            if (parsed.dependencies) {
                for (const [name, dep] of Object.entries(parsed.dependencies)) {
                    if (typeof dep === 'string') {
                        dependencies[name] = dep;
                    }
                    else if (typeof dep === 'object' && dep && 'version' in dep) {
                        dependencies[name] = dep.version;
                    }
                }
            }
            // Parse dev dependencies
            if (parsed['dev-dependencies']) {
                for (const [name, dep] of Object.entries(parsed['dev-dependencies'])) {
                    if (typeof dep === 'string') {
                        devDependencies[name] = dep;
                    }
                    else if (typeof dep === 'object' && dep && 'version' in dep) {
                        devDependencies[name] = dep.version;
                    }
                }
            }
            return {
                name: parsed.package?.name || 'rust-project',
                version: parsed.package?.version || '0.1.0',
                dependencies,
                devDependencies,
                scripts: {} // Rust doesn't have scripts like npm
            };
        }
        catch (error) {
            this.logger.debug('Failed to parse Cargo.toml', {
                filePath,
                error: error.message
            });
            return {
                name: 'rust-project',
                version: '0.1.0',
                dependencies: {},
                devDependencies: {},
                scripts: {}
            };
        }
    }
    async parseGoMod(filePath) {
        const content = await fs_extra_1.default.readFile(filePath, 'utf8');
        const dependencies = {};
        const lines = content.split('\n');
        let inRequireBlock = false;
        let moduleName = 'go-project';
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed.startsWith('module ')) {
                moduleName = trimmed.replace('module ', '');
            }
            else if (trimmed === 'require (') {
                inRequireBlock = true;
            }
            else if (trimmed === ')' && inRequireBlock) {
                inRequireBlock = false;
            }
            else if (inRequireBlock || trimmed.startsWith('require ')) {
                const match = trimmed.match(/^(?:require\s+)?([^\s]+)\s+([^\s]+)/);
                if (match) {
                    dependencies[match[1]] = match[2];
                }
            }
        }
        return {
            name: moduleName,
            version: '0.0.0',
            dependencies,
            devDependencies: {},
            scripts: {}
        };
    }
    async parsePomXml(filePath) {
        // This would require an XML parser in a real implementation
        // For now, return basic info
        return {
            name: 'java-project',
            version: '1.0.0',
            dependencies: {},
            devDependencies: {},
            scripts: {}
        };
    }
    extractDependencies(packageInfo) {
        if (!packageInfo) {
            return {};
        }
        return {
            ...packageInfo.dependencies,
            ...packageInfo.devDependencies
        };
    }
    async updateFileIndex(rootPath, relativePath) {
        try {
            const fullPath = path_1.default.resolve(rootPath, relativePath);
            if (!await fs_extra_1.default.pathExists(fullPath)) {
                return null;
            }
            const stats = await fs_extra_1.default.stat(fullPath);
            const fileInfo = {
                path: relativePath,
                type: stats.isDirectory() ? 'directory' : 'file',
                size: stats.size,
                lastModified: stats.mtime,
                permissions: stats.mode.toString(8)
            };
            // Add content for text files
            if (fileInfo.type === 'file' && this.isTextFile(fullPath)) {
                const config = this.configManager.getConfig().context;
                if (stats.size <= config.maxFileSize) {
                    try {
                        fileInfo.content = await fs_extra_1.default.readFile(fullPath, 'utf8');
                    }
                    catch (error) {
                        this.logger.debug('Failed to read file content', {
                            filePath: fullPath,
                            error: error.message
                        });
                    }
                }
            }
            this.logger.debug('File index updated', {
                path: relativePath,
                type: fileInfo.type,
                size: fileInfo.size
            });
            return fileInfo;
        }
        catch (error) {
            this.logger.debug('Failed to update file index', {
                rootPath,
                relativePath,
                error: error.message
            });
            return null;
        }
    }
}
exports.ProjectIndexer = ProjectIndexer;
//# sourceMappingURL=ProjectIndexer.js.map