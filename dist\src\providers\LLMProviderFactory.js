"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LLMProviderFactory = void 0;
const ErrorHandler_1 = require("../utils/ErrorHandler");
const Logger_1 = require("../utils/Logger");
const ConfigManager_1 = require("../config/ConfigManager");
const OpenAIProvider_1 = require("./OpenAIProvider");
const AnthropicProvider_1 = require("./AnthropicProvider");
const DeepseekProvider_1 = require("./DeepseekProvider");
const OllamaProvider_1 = require("./OllamaProvider");
const GeminiProvider_1 = require("./GeminiProvider");
const MistralProvider_1 = require("./MistralProvider");
class LLMProviderFactory {
    static instance;
    providers;
    logger;
    configManager;
    constructor() {
        this.providers = new Map();
        this.logger = Logger_1.Logger.getInstance();
        this.configManager = ConfigManager_1.ConfigManager.getInstance();
    }
    static getInstance() {
        if (!LLMProviderFactory.instance) {
            LLMProviderFactory.instance = new LLMProviderFactory();
        }
        return LLMProviderFactory.instance;
    }
    async createProvider(providerName) {
        // Check if provider is already cached
        if (this.providers.has(providerName)) {
            return this.providers.get(providerName);
        }
        const config = this.configManager.getProviderConfig(providerName);
        if (!config) {
            throw new ErrorHandler_1.ConfigurationError(`No configuration found for provider: ${providerName}`);
        }
        let provider;
        try {
            switch (providerName.toLowerCase()) {
                case 'openai':
                    provider = new OpenAIProvider_1.OpenAIProvider(config);
                    break;
                case 'anthropic':
                    provider = new AnthropicProvider_1.AnthropicProvider(config);
                    break;
                case 'deepseek':
                    provider = new DeepseekProvider_1.DeepseekProvider(config);
                    break;
                case 'ollama':
                    provider = new OllamaProvider_1.OllamaProvider(config);
                    break;
                case 'gemini':
                    provider = new GeminiProvider_1.GeminiProvider(config);
                    break;
                case 'mistral':
                    provider = new MistralProvider_1.MistralProvider(config);
                    break;
                default:
                    throw new ErrorHandler_1.LLMProviderError(providerName, `Unsupported provider: ${providerName}`);
            }
            // Test the provider connection
            await this.testProvider(provider);
            // Cache the provider
            this.providers.set(providerName, provider);
            this.logger.info(`LLM provider created and tested: ${providerName}`);
            return provider;
        }
        catch (error) {
            this.logger.error(`Failed to create LLM provider: ${providerName}`, {
                error: error.message,
                provider: providerName
            });
            if (error instanceof ErrorHandler_1.LLMProviderError || error instanceof ErrorHandler_1.ConfigurationError) {
                throw error;
            }
            throw new ErrorHandler_1.LLMProviderError(providerName, `Failed to initialize provider: ${error.message}`);
        }
    }
    async getProvider(providerName) {
        const targetProvider = providerName || this.configManager.getAgentConfig().provider;
        return this.createProvider(targetProvider);
    }
    getSupportedProviders() {
        return ['openai', 'anthropic', 'deepseek', 'ollama', 'gemini', 'mistral'];
    }
    isProviderSupported(providerName) {
        return this.getSupportedProviders().includes(providerName.toLowerCase());
    }
    async testProvider(provider) {
        try {
            const testPrompt = 'Hello, this is a test message. Please respond with "Test successful".';
            const response = await provider.generateResponse(testPrompt, {
                maxTokens: 50,
                temperature: 0
            });
            if (!response.content) {
                throw new Error('Provider returned empty response');
            }
            this.logger.debug(`Provider test successful: ${provider.name}`, {
                provider: provider.name,
                responseLength: response.content.length
            });
            return true;
        }
        catch (error) {
            this.logger.error(`Provider test failed: ${provider.name}`, {
                provider: provider.name,
                error: error.message
            });
            throw new ErrorHandler_1.LLMProviderError(provider.name, `Provider test failed: ${error.message}`);
        }
    }
    async testAllProviders() {
        const results = {};
        const supportedProviders = this.getSupportedProviders();
        for (const providerName of supportedProviders) {
            try {
                const config = this.configManager.getProviderConfig(providerName);
                if (!config) {
                    results[providerName] = false;
                    continue;
                }
                // Skip providers without API keys (except Ollama)
                if (providerName !== 'ollama' && !config.apiKey) {
                    results[providerName] = false;
                    continue;
                }
                const provider = await this.createProvider(providerName);
                results[providerName] = await this.testProvider(provider);
            }
            catch (error) {
                this.logger.debug(`Provider test failed: ${providerName}`, {
                    provider: providerName,
                    error: error.message
                });
                results[providerName] = false;
            }
        }
        return results;
    }
    clearProviderCache(providerName) {
        if (providerName) {
            this.providers.delete(providerName);
            this.logger.info(`Provider cache cleared: ${providerName}`);
        }
        else {
            this.providers.clear();
            this.logger.info('All provider caches cleared');
        }
    }
    getProviderCapabilities(providerName) {
        const capabilities = {
            openai: {
                supportsToolCalling: true,
                supportsStreaming: true,
                maxTokens: 128000,
                supportedModels: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo', 'gpt-4o', 'gpt-4o-mini']
            },
            anthropic: {
                supportsToolCalling: true,
                supportsStreaming: true,
                maxTokens: 200000,
                supportedModels: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307', 'claude-3-5-sonnet-20241022']
            },
            deepseek: {
                supportsToolCalling: true,
                supportsStreaming: true,
                maxTokens: 64000,
                supportedModels: ['deepseek-chat', 'deepseek-coder']
            },
            ollama: {
                supportsToolCalling: true,
                supportsStreaming: true,
                maxTokens: 32000,
                supportedModels: ['llama2', 'llama3', 'codellama', 'mistral', 'mixtral']
            },
            gemini: {
                supportsToolCalling: true,
                supportsStreaming: true,
                maxTokens: 32000,
                supportedModels: ['gemini-pro', 'gemini-pro-vision']
            },
            mistral: {
                supportsToolCalling: true,
                supportsStreaming: true,
                maxTokens: 32000,
                supportedModels: ['mistral-tiny', 'mistral-small', 'mistral-medium', 'mistral-large']
            }
        };
        return capabilities[providerName.toLowerCase()] || {
            supportsToolCalling: false,
            supportsStreaming: false,
            maxTokens: 4000,
            supportedModels: []
        };
    }
    async switchProvider(newProviderName) {
        if (!this.isProviderSupported(newProviderName)) {
            throw new ErrorHandler_1.LLMProviderError(newProviderName, `Unsupported provider: ${newProviderName}`);
        }
        // Update configuration
        this.configManager.updateAgentConfig({ provider: newProviderName });
        // Clear old provider cache
        this.clearProviderCache();
        // Create and return new provider
        const provider = await this.createProvider(newProviderName);
        this.logger.info(`Switched to provider: ${newProviderName}`);
        return provider;
    }
    getProviderInfo(providerName) {
        const providerInfo = {
            openai: {
                name: 'OpenAI',
                description: 'Advanced AI models including GPT-4 and GPT-3.5',
                website: 'https://openai.com',
                pricing: 'Pay-per-token',
                features: ['Function calling', 'Code generation', 'Reasoning', 'Vision']
            },
            anthropic: {
                name: 'Anthropic Claude',
                description: 'Constitutional AI with strong reasoning capabilities',
                website: 'https://anthropic.com',
                pricing: 'Pay-per-token',
                features: ['Function calling', 'Long context', 'Safety-focused', 'Reasoning']
            },
            deepseek: {
                name: 'DeepSeek',
                description: 'Efficient AI models with strong coding capabilities',
                website: 'https://deepseek.com',
                pricing: 'Pay-per-token',
                features: ['Code generation', 'Function calling', 'Cost-effective']
            },
            ollama: {
                name: 'Ollama',
                description: 'Local AI models running on your machine',
                website: 'https://ollama.ai',
                pricing: 'Free (local)',
                features: ['Local execution', 'Privacy', 'No API costs', 'Offline capable']
            },
            gemini: {
                name: 'Google Gemini',
                description: 'Google\'s multimodal AI with strong reasoning',
                website: 'https://ai.google.dev',
                pricing: 'Pay-per-token',
                features: ['Multimodal', 'Function calling', 'Fast inference']
            },
            mistral: {
                name: 'Mistral AI',
                description: 'European AI with strong performance and efficiency',
                website: 'https://mistral.ai',
                pricing: 'Pay-per-token',
                features: ['Function calling', 'Multilingual', 'Efficient']
            }
        };
        return providerInfo[providerName.toLowerCase()] || {
            name: 'Unknown',
            description: 'Unknown provider',
            website: '',
            pricing: 'Unknown',
            features: []
        };
    }
}
exports.LLMProviderFactory = LLMProviderFactory;
//# sourceMappingURL=LLMProviderFactory.js.map