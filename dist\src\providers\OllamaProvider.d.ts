import { LLMProvider, LLMOptions, LLMResponse, Tool<PERSON><PERSON>, Too<PERSON><PERSON><PERSON>ult, AgentContext } from '../../types';
export declare class OllamaProvider implements LLMProvider {
    readonly name = "ollama";
    private baseURL;
    private timeout;
    private logger;
    private toolRegistry;
    constructor(config: Record<string, unknown>);
    generateResponse(prompt: string, options?: LLMOptions): Promise<LLMResponse>;
    generateStreamResponse(prompt: string, options?: LLMOptions): AsyncGenerator<string, void, unknown>;
    supportsToolCalling(): boolean;
    callTool(toolCall: ToolCall, context: AgentContext): Promise<ToolResult>;
    private buildMessages;
    validateConnection(): Promise<boolean>;
    getAvailableModels(): Promise<string[]>;
    getDefaultModel(): string;
    getMaxTokens(_model?: string): number;
    estimateTokens(text: string): number;
    calculateCost(_usage: {
        promptTokens: number;
        completionTokens: number;
    }, _model?: string): number;
    private buildToolDescriptions;
    private parseToolCalls;
}
//# sourceMappingURL=OllamaProvider.d.ts.map