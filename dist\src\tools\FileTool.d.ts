import { Tool, ToolResult, AgentContext } from '../types';
export declare class FileTool implements Tool {
    readonly name = "file";
    readonly description = "Comprehensive file operations including read, write, search, and manipulation";
    readonly parameters: {
        type: "object";
        properties: {
            operation: {
                type: string;
                description: string;
                enum: string[];
            };
            path: {
                type: string;
                description: string;
            };
            content: {
                type: string;
                description: string;
            };
            destination: {
                type: string;
                description: string;
            };
            pattern: {
                type: string;
                description: string;
            };
            options: {
                type: string;
                description: string;
            };
        };
        required: string[];
    };
    private logger;
    constructor();
    execute(args: Record<string, unknown>, context: AgentContext): Promise<ToolResult>;
    private readFile;
    private writeFile;
    private appendFile;
    private deleteFile;
    private copyFile;
    private moveFile;
    private createDirectory;
    private removeDirectory;
    private checkExists;
    private getStats;
    private getPermissions;
    private searchInFiles;
    private globFiles;
    private watchFile;
    private backupFile;
    private restoreFile;
    private compressFile;
    private extractFile;
    private syncDirectories;
    private diffFiles;
    private mergeFiles;
    private splitFile;
    private hashFile;
    private findFiles;
    private replaceInFiles;
    private batchOperation;
}
//# sourceMappingURL=FileTool.d.ts.map