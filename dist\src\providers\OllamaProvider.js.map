{"version": 3, "file": "OllamaProvider.js", "sourceRoot": "", "sources": ["../../../src/providers/OllamaProvider.ts"], "names": [], "mappings": ";;;;;;AAAA,4DAA+B;AAE/B,uDAAwD;AACxD,2CAAwC;AACxC,uDAAoD;AAEpD,MAAa,cAAc;IACT,IAAI,GAAG,QAAQ,CAAC;IACxB,OAAO,CAAS;IAChB,OAAO,CAAS;IAChB,MAAM,CAAS;IACf,YAAY,CAAe;IAEnC,YAAY,MAA+B;QACzC,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG,2BAAY,CAAC,WAAW,EAAE,CAAC;QAC/C,IAAI,CAAC,OAAO,GAAI,MAAM,CAAC,OAAkB,IAAI,wBAAwB,CAAC;QACtE,IAAI,CAAC,OAAO,GAAI,MAAM,CAAC,OAAkB,IAAI,KAAK,CAAC;IACrD,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,UAAsB,EAAE;QACpE,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,QAAQ,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAErD,MAAM,WAAW,GAAG;gBAClB,KAAK;gBACL,QAAQ;gBACR,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE;oBACP,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,GAAG;oBACvC,WAAW,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;iBACvC;aACF,CAAC;YAEF,gEAAgE;YAChE,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9C,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACnE,MAAM,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;gBAC9D,IAAI,aAAa,EAAE,CAAC;oBAClB,aAAa,CAAC,OAAO,IAAI,OAAS,gBAAgB,EAAE,CAAC;gBACvD,CAAC;qBAAM,CAAC;oBACN,QAAQ,CAAC,OAAO,CAAC;wBACf,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,gBAAgB;qBAC1B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBAC7C,KAAK;gBACL,YAAY,EAAE,QAAQ,CAAC,MAAM;gBAC7B,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAA,oBAAK,EAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;gBACvD,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;gBACjC,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;aAC1C,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,QAAQ,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YACrE,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;YAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC;YAE5C,wDAAwD;YACxD,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC3D,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAEpC,MAAM,WAAW,GAAgB;gBAC/B,OAAO;gBACP,SAAS;gBACT,KAAK,EAAE;oBACL,YAAY,EAAE,IAAI,CAAC,iBAAiB,IAAI,CAAC;oBACzC,gBAAgB,EAAE,IAAI,CAAC,UAAU,IAAI,CAAC;oBACtC,WAAW,EAAE,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;iBACpE;aACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;YAE7E,OAAO,WAAW,CAAC;QAErB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBAC7C,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,MAAM,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAG,KAAK;gBAC1C,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;YAEH,MAAM,IAAI,+BAAgB,CAAC,IAAI,CAAC,IAAI,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,CAAC,sBAAsB,CAAC,MAAc,EAAE,UAAsB,EAAE;QAC3E,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,QAAQ,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAErD,MAAM,WAAW,GAAG;gBAClB,KAAK;gBACL,QAAQ;gBACR,MAAM,EAAE,IAAI;gBACZ,OAAO,EAAE;oBACP,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,GAAG;oBACvC,WAAW,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;iBACvC;aACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACvD,KAAK;gBACL,YAAY,EAAE,QAAQ,CAAC,MAAM;gBAC7B,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAA,oBAAK,EAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;gBACvD,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;gBACjC,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;aAC1C,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,QAAQ,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YACrE,CAAC;YAED,MAAM,MAAM,GAAI,QAAQ,CAAC,IAAY,EAAE,SAAS,EAAE,CAAC;YACnD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;YACvD,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,IAAI,MAAM,GAAG,EAAE,CAAC;YAEhB,OAAO,IAAI,EAAE,CAAC;gBACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;gBAC5C,IAAI,IAAI;oBAAE,MAAM;gBAEhB,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;gBAClD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACjC,MAAM,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;gBAE3B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACzB,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC;wBAChB,IAAI,CAAC;4BACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;4BAC9B,IAAI,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;gCAC1B,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;4BAC7B,CAAC;wBACH,CAAC;wBAAC,MAAM,CAAC;4BACP,0BAA0B;wBAC5B,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACvD,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,MAAM,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAG,KAAK;gBAC1C,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;YAEH,MAAM,IAAI,+BAAgB,CAAC,IAAI,CAAC,IAAI,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAEM,mBAAmB;QACxB,OAAO,IAAI,CAAC,CAAC,0DAA0D;IACzE,CAAC;IAEM,KAAK,CAAC,QAAQ,CAAC,QAAkB,EAAE,OAAqB;QAC7D,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE;gBAC5D,QAAQ,EAAE,QAAQ,CAAC,IAAI;gBACvB,KAAK,EAAG,KAAe,CAAC,OAAO;aAChC,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,aAAa,CAAC,MAAc,EAAE,OAAmB;QACvD,MAAM,QAAQ,GAAU,EAAE,CAAC;QAE3B,iCAAiC;QACjC,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,OAAO,CAAC,YAAY;aAC9B,CAAC,CAAC;QACL,CAAC;QAED,uCAAuC;QACvC,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;YAChC,KAAK,MAAM,GAAG,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;gBAC9C,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC,6BAA6B;oBACtD,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,GAAG,CAAC,IAAI;wBACd,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,qBAAqB;QACrB,QAAQ,CAAC,IAAI,CAAC;YACZ,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,MAAM;SAChB,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEM,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAA,oBAAK,EAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;gBACvD,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC;aAClC,CAAC,CAAC;YACH,OAAO,QAAQ,CAAC,EAAE,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACvD,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAA,oBAAK,EAAC,GAAG,IAAI,CAAC,OAAO,WAAW,CAAC,CAAC;YACzD,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,QAAQ,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YACrE,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;YAC1C,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBACjD,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;YACH,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAEM,eAAe;QACpB,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEM,YAAY,CAAC,MAAe;QACjC,+CAA+C;QAC/C,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,cAAc,CAAC,IAAY;QAChC,4DAA4D;QAC5D,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACpC,CAAC;IAEM,aAAa,CAAC,MAA0D,EAAE,MAAe;QAC9F,mCAAmC;QACnC,OAAO,CAAC,CAAC;IACX,CAAC;IAEO,qBAAqB,CAAC,KAAY;QACxC,MAAM,YAAY,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACpC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;gBACzC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC;qBACvC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAgB,EAAE,EAAE,CAAC,GAAG,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;qBACpE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAErB,OAAO,GAAG,IAAI,CAAC,IAAI,IAAI,MAAM,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,OAAO;EACT,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;6GAMoF,CAAC;IAC5G,CAAC;IAEO,cAAc,CAAC,OAAe;QACpC,MAAM,SAAS,GAAe,EAAE,CAAC;QAEjC,IAAI,CAAC;YACH,iDAAiD;YACjD,MAAM,SAAS,GAAG,8BAA8B,CAAC;YACjD,MAAM,UAAU,GAAG,kCAAkC,CAAC;YAEtD,IAAI,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACxC,IAAI,OAAO,EAAE,CAAC;gBACZ,sBAAsB;gBACtB,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;oBAC5B,IAAI,CAAC;wBACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;wBACjC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;4BAC1B,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE,CAAC;gCAC1B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oCACnB,SAAS,CAAC,IAAI,CAAC;wCACb,EAAE,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;wCACrE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;wCACzB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,EAAE;qCAC1C,CAAC,CAAC;gCACL,CAAC;4BACH,CAAC;wBACH,CAAC;oBACH,CAAC;oBAAC,MAAM,CAAC;wBACP,oBAAoB;oBACtB,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,8BAA8B;gBAC9B,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBACnC,IAAI,OAAO,EAAE,CAAC;oBACZ,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;wBAC5B,IAAI,CAAC;4BACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;4BACjC,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;gCACrB,SAAS,CAAC,IAAI,CAAC;oCACb,EAAE,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;oCACrE,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI;oCAC3B,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,SAAS,IAAI,EAAE;iCAC5C,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,MAAM,CAAC;4BACP,oBAAoB;wBACtB,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iDAAiD,EAAE;gBACnE,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;aACnC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AA3VD,wCA2VC"}