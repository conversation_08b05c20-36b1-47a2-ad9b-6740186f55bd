export declare class <PERSON><PERSON>liError extends <PERSON>rror {
    readonly code: string;
    readonly context?: Record<string, any>;
    constructor(message: string, code: string, context?: Record<string, any>);
}
export declare class ToolExecutionError extends AICliError {
    constructor(toolName: string, message: string, context?: Record<string, any>);
}
export declare class LL<PERSON>roviderError extends AICliError {
    constructor(provider: string, message: string, context?: Record<string, any>);
}
export declare class SessionError extends AICliError {
    constructor(message: string, context?: Record<string, any>);
}
export declare class ConfigurationError extends AICliError {
    constructor(message: string, context?: Record<string, any>);
}
export declare class FileOperationError extends AICliError {
    constructor(operation: string, path: string, message: string, context?: Record<string, any>);
}
export declare class ShellExecutionError extends AICliError {
    constructor(command: string, exitCode: number, stderr: string, context?: Record<string, any>);
}
export declare class ErrorHandler {
    private static instance;
    private logger;
    private constructor();
    static getInstance(): ErrorHandler;
    handleError(error: Error, context?: string): void;
    private handleAICliError;
    private handleGenericError;
    handleAsyncError<T>(operation: () => Promise<T>, context?: string): Promise<T | null>;
    handleSyncError<T>(operation: () => T, context?: string): T | null;
    createGracefulShutdown(): void;
}
//# sourceMappingURL=ErrorHandler.d.ts.map