"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileTool = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
const fast_glob_1 = __importDefault(require("fast-glob"));
const archiver_1 = __importDefault(require("archiver"));
const unzipper_1 = __importDefault(require("unzipper"));
const ErrorHandler_1 = require("../utils/ErrorHandler");
const Logger_1 = require("../utils/Logger");
class FileTool {
    name = 'file';
    description = 'Comprehensive file operations including read, write, search, and manipulation';
    parameters = {
        type: 'object',
        properties: {
            operation: {
                type: 'string',
                description: 'File operation to perform',
                enum: ['read', 'write', 'append', 'delete', 'copy', 'move', 'mkdir', 'rmdir', 'exists', 'stat', 'search', 'glob', 'permissions', 'watch', 'backup', 'restore', 'compress', 'extract', 'sync']
            },
            path: {
                type: 'string',
                description: 'File or directory path'
            },
            content: {
                type: 'string',
                description: 'Content to write (for write/append operations)'
            },
            destination: {
                type: 'string',
                description: 'Destination path (for copy/move operations)'
            },
            pattern: {
                type: 'string',
                description: 'Search pattern or glob pattern'
            },
            options: {
                type: 'object',
                description: 'Additional options for the operation'
            }
        },
        required: ['operation', 'path']
    };
    logger;
    constructor() {
        this.logger = Logger_1.Logger.getInstance();
    }
    async execute(args, context) {
        const startTime = Date.now();
        try {
            const absolutePath = path_1.default.resolve(context.workingDirectory, args.path);
            const options = args.options || {};
            let result;
            switch (args.operation) {
                case 'read':
                    result = await this.readFile(absolutePath, options);
                    break;
                case 'write':
                    result = await this.writeFile(absolutePath, args.content, options);
                    break;
                case 'append':
                    result = await this.appendFile(absolutePath, args.content, options);
                    break;
                case 'delete':
                    result = await this.deleteFile(absolutePath, options);
                    break;
                case 'copy':
                    result = await this.copyFile(absolutePath, args.destination, options);
                    break;
                case 'move':
                    result = await this.moveFile(absolutePath, args.destination, options);
                    break;
                case 'mkdir':
                    result = await this.createDirectory(absolutePath, options);
                    break;
                case 'rmdir':
                    result = await this.removeDirectory(absolutePath, options);
                    break;
                case 'exists':
                    result = await this.checkExists(absolutePath);
                    break;
                case 'stat':
                    result = await this.getStats(absolutePath);
                    break;
                case 'search':
                    const searchOptions = { pattern: args.pattern, ...options };
                    const searchResults = await this.searchInFiles(absolutePath, searchOptions);
                    result = {
                        success: true,
                        path: absolutePath,
                        operation: 'search',
                        metadata: { results: searchResults, count: searchResults.length }
                    };
                    break;
                case 'glob':
                    const globResults = await this.globFiles(args.pattern, context.workingDirectory);
                    result = {
                        success: true,
                        path: context.workingDirectory,
                        operation: 'glob',
                        metadata: { files: globResults, count: globResults.length }
                    };
                    break;
                case 'permissions':
                    result = await this.getPermissions(absolutePath);
                    break;
                case 'watch':
                    result = await this.watchFile(absolutePath, options);
                    break;
                case 'backup':
                    result = await this.backupFile(absolutePath, args.destination, options);
                    break;
                case 'restore':
                    result = await this.restoreFile(absolutePath, args.destination, options);
                    break;
                case 'compress':
                    result = await this.compressFile(absolutePath, args.destination, options);
                    break;
                case 'extract':
                    result = await this.extractFile(absolutePath, args.destination, options);
                    break;
                case 'sync':
                    result = await this.syncDirectories(absolutePath, args.destination, options);
                    break;
                case 'diff':
                    result = await this.diffFiles(absolutePath, args.destination, options);
                    break;
                case 'merge':
                    result = await this.mergeFiles(args.files, absolutePath, options);
                    break;
                case 'split':
                    result = await this.splitFile(absolutePath, args.chunkSize, options);
                    break;
                case 'hash':
                    result = await this.hashFile(absolutePath, args.algorithm, options);
                    break;
                case 'find':
                    result = await this.findFiles(args.criteria, absolutePath, options);
                    break;
                case 'replace':
                    result = await this.replaceInFiles(args.pattern, args.replacement, absolutePath, options);
                    break;
                case 'batch':
                    result = await this.batchOperation(args.operations, context, options);
                    break;
                default:
                    throw new ErrorHandler_1.FileOperationError(args.operation, absolutePath, 'Unknown operation');
            }
            const duration = Date.now() - startTime;
            this.logger.logToolExecution(this.name, args, result, duration);
            return {
                success: result.success,
                result: result,
                metadata: {
                    operation: args.operation,
                    path: absolutePath,
                    duration
                }
            };
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error(`File operation failed: ${args.operation} on ${args.path}`, {
                error: error.message,
                operation: args.operation,
                path: args.path,
                duration
            });
            if (error instanceof ErrorHandler_1.FileOperationError) {
                throw error;
            }
            throw new ErrorHandler_1.FileOperationError(args.operation, args.path, error.message, { duration });
        }
    }
    async readFile(filePath, options) {
        try {
            const content = await fs_extra_1.default.readFile(filePath, (options.encoding || 'utf8'));
            const stats = await fs_extra_1.default.stat(filePath);
            return {
                success: true,
                path: filePath,
                operation: 'read',
                size: stats.size,
                metadata: { content, encoding: options.encoding || 'utf8' }
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('read', filePath, error.message);
        }
    }
    async writeFile(filePath, content, options) {
        try {
            if (options.createDirectories) {
                await fs_extra_1.default.ensureDir(path_1.default.dirname(filePath));
            }
            if (!options.overwrite && await fs_extra_1.default.pathExists(filePath)) {
                throw new Error('File exists and overwrite is disabled');
            }
            await fs_extra_1.default.writeFile(filePath, content, (options.encoding || 'utf8'));
            const stats = await fs_extra_1.default.stat(filePath);
            return {
                success: true,
                path: filePath,
                operation: 'write',
                size: stats.size
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('write', filePath, error.message);
        }
    }
    async appendFile(filePath, content, options) {
        try {
            if (options.createDirectories) {
                await fs_extra_1.default.ensureDir(path_1.default.dirname(filePath));
            }
            await fs_extra_1.default.appendFile(filePath, content, (options.encoding || 'utf8'));
            const stats = await fs_extra_1.default.stat(filePath);
            return {
                success: true,
                path: filePath,
                operation: 'append',
                size: stats.size
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('append', filePath, error.message);
        }
    }
    async deleteFile(filePath, options) {
        try {
            if (options.force) {
                await fs_extra_1.default.remove(filePath);
            }
            else {
                await fs_extra_1.default.unlink(filePath);
            }
            return {
                success: true,
                path: filePath,
                operation: 'delete'
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('delete', filePath, error.message);
        }
    }
    async copyFile(sourcePath, destPath, options) {
        try {
            const absoluteDestPath = path_1.default.resolve(path_1.default.dirname(sourcePath), destPath);
            if (options.createDirectories) {
                await fs_extra_1.default.ensureDir(path_1.default.dirname(absoluteDestPath));
            }
            const copyOptions = {};
            if (options.overwrite !== undefined) {
                copyOptions.overwrite = options.overwrite;
            }
            if (options.preserveTimestamps) {
                copyOptions.preserveTimestamps = true;
            }
            await fs_extra_1.default.copy(sourcePath, absoluteDestPath, copyOptions);
            const stats = await fs_extra_1.default.stat(absoluteDestPath);
            return {
                success: true,
                path: absoluteDestPath,
                operation: 'copy',
                size: stats.size,
                metadata: { source: sourcePath, destination: absoluteDestPath }
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('copy', sourcePath, error.message);
        }
    }
    async moveFile(sourcePath, destPath, options) {
        try {
            const absoluteDestPath = path_1.default.resolve(path_1.default.dirname(sourcePath), destPath);
            if (options.createDirectories) {
                await fs_extra_1.default.ensureDir(path_1.default.dirname(absoluteDestPath));
            }
            await fs_extra_1.default.move(sourcePath, absoluteDestPath, { overwrite: options.overwrite });
            const stats = await fs_extra_1.default.stat(absoluteDestPath);
            return {
                success: true,
                path: absoluteDestPath,
                operation: 'move',
                size: stats.size,
                metadata: { source: sourcePath, destination: absoluteDestPath }
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('move', sourcePath, error.message);
        }
    }
    async createDirectory(dirPath, options) {
        try {
            if (options.recursive !== false) {
                await fs_extra_1.default.ensureDir(dirPath);
            }
            else {
                await fs_extra_1.default.mkdir(dirPath);
            }
            return {
                success: true,
                path: dirPath,
                operation: 'mkdir'
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('mkdir', dirPath, error.message);
        }
    }
    async removeDirectory(dirPath, options) {
        try {
            if (options.recursive !== false) {
                await fs_extra_1.default.remove(dirPath);
            }
            else {
                await fs_extra_1.default.rmdir(dirPath);
            }
            return {
                success: true,
                path: dirPath,
                operation: 'rmdir'
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('rmdir', dirPath, error.message);
        }
    }
    async checkExists(filePath) {
        try {
            const exists = await fs_extra_1.default.pathExists(filePath);
            return {
                success: true,
                path: filePath,
                operation: 'exists',
                metadata: { exists }
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('exists', filePath, error.message);
        }
    }
    async getStats(filePath) {
        try {
            const stats = await fs_extra_1.default.stat(filePath);
            return {
                success: true,
                path: filePath,
                operation: 'stat',
                size: stats.size,
                metadata: {
                    isFile: stats.isFile(),
                    isDirectory: stats.isDirectory(),
                    size: stats.size,
                    created: stats.birthtime,
                    modified: stats.mtime,
                    accessed: stats.atime,
                    mode: stats.mode,
                    uid: stats.uid,
                    gid: stats.gid
                }
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('stat', filePath, error.message);
        }
    }
    async getPermissions(filePath) {
        try {
            const stats = await fs_extra_1.default.stat(filePath);
            const mode = stats.mode;
            const permissions = {
                owner: {
                    read: !!(mode & parseInt('400', 8)),
                    write: !!(mode & parseInt('200', 8)),
                    execute: !!(mode & parseInt('100', 8))
                },
                group: {
                    read: !!(mode & parseInt('040', 8)),
                    write: !!(mode & parseInt('020', 8)),
                    execute: !!(mode & parseInt('010', 8))
                },
                others: {
                    read: !!(mode & parseInt('004', 8)),
                    write: !!(mode & parseInt('002', 8)),
                    execute: !!(mode & parseInt('001', 8))
                }
            };
            return {
                success: true,
                path: filePath,
                operation: 'permissions',
                metadata: { permissions, mode: mode.toString(8) }
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('permissions', filePath, error.message);
        }
    }
    async searchInFiles(searchPath, options) {
        const results = [];
        const files = await (0, fast_glob_1.default)(options.includeFiles || ['**/*'], {
            cwd: searchPath,
            ignore: options.excludeFiles || [],
            onlyFiles: true,
            absolute: true
        });
        for (const file of files) {
            try {
                const content = await fs_extra_1.default.readFile(file, 'utf8');
                const lines = content.split('\n');
                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i];
                    if (!line)
                        continue;
                    let match = null;
                    if (options.regex) {
                        const flags = options.caseSensitive ? 'g' : 'gi';
                        const regex = new RegExp(options.pattern, flags);
                        match = line.match(regex);
                    }
                    else {
                        const searchText = options.caseSensitive ? line : line.toLowerCase();
                        const pattern = options.caseSensitive ? options.pattern : options.pattern.toLowerCase();
                        if (options.wholeWord) {
                            const regex = new RegExp(`\\b${pattern}\\b`, options.caseSensitive ? 'g' : 'gi');
                            match = line.match(regex);
                        }
                        else {
                            if (searchText && searchText.includes(pattern)) {
                                match = [pattern];
                            }
                        }
                    }
                    if (match) {
                        const contextStart = Math.max(0, i - (options.context || 0));
                        const contextEnd = Math.min(lines.length - 1, i + (options.context || 0));
                        const context = lines.slice(contextStart, contextEnd + 1);
                        results.push({
                            file,
                            line: i + 1,
                            column: line.indexOf(match[0]) + 1,
                            match: match[0],
                            context
                        });
                        if (options.maxResults && results.length >= options.maxResults) {
                            return results;
                        }
                    }
                }
            }
            catch (error) {
                // Skip files that can't be read
                continue;
            }
        }
        return results;
    }
    async globFiles(pattern, cwd) {
        try {
            return await (0, fast_glob_1.default)(pattern, { cwd, absolute: true });
        }
        catch (error) {
            throw new Error(`Glob pattern failed: ${error.message}`);
        }
    }
    async watchFile(filePath, options) {
        try {
            // This is a simplified implementation - in a real scenario, you'd set up file watchers
            const stats = await fs_extra_1.default.stat(filePath);
            return {
                success: true,
                path: filePath,
                operation: 'watch',
                metadata: {
                    message: 'File watching initiated (simplified implementation)',
                    lastModified: stats.mtime,
                    size: stats.size
                }
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('watch', filePath, error.message);
        }
    }
    async backupFile(filePath, backupPath, options) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const finalBackupPath = backupPath || `${filePath}.backup.${timestamp}`;
            await fs_extra_1.default.copy(filePath, finalBackupPath, {
                preserveTimestamps: options.preserveTimestamps !== false
            });
            const stats = await fs_extra_1.default.stat(finalBackupPath);
            return {
                success: true,
                path: finalBackupPath,
                operation: 'backup',
                size: stats.size,
                metadata: {
                    originalPath: filePath,
                    backupPath: finalBackupPath,
                    timestamp
                }
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('backup', filePath, error.message);
        }
    }
    async restoreFile(backupPath, originalPath, options) {
        try {
            if (!await fs_extra_1.default.pathExists(backupPath)) {
                throw new Error('Backup file does not exist');
            }
            if (!options.overwrite && await fs_extra_1.default.pathExists(originalPath)) {
                throw new Error('Target file exists and overwrite is disabled');
            }
            await fs_extra_1.default.copy(backupPath, originalPath, {
                preserveTimestamps: options.preserveTimestamps !== false
            });
            const stats = await fs_extra_1.default.stat(originalPath);
            return {
                success: true,
                path: originalPath,
                operation: 'restore',
                size: stats.size,
                metadata: {
                    backupPath,
                    originalPath
                }
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('restore', backupPath, error.message);
        }
    }
    async compressFile(filePath, outputPath, options) {
        try {
            const stats = await fs_extra_1.default.stat(filePath);
            const isDirectory = stats.isDirectory();
            const format = options.format || 'zip';
            const finalOutputPath = outputPath || `${filePath}.${format}`;
            return new Promise((resolve, reject) => {
                const output = fs_extra_1.default.createWriteStream(finalOutputPath);
                const archive = (0, archiver_1.default)(format, {
                    zlib: { level: options.compressionLevel || 9 }
                });
                output.on('close', async () => {
                    try {
                        const compressedStats = await fs_extra_1.default.stat(finalOutputPath);
                        resolve({
                            success: true,
                            path: finalOutputPath,
                            operation: 'compress',
                            size: compressedStats.size,
                            metadata: {
                                originalPath: filePath,
                                compressedPath: finalOutputPath,
                                originalSize: stats.size,
                                compressedSize: compressedStats.size,
                                compressionRatio: compressedStats.size / stats.size,
                                format,
                                totalBytes: archive.pointer()
                            }
                        });
                    }
                    catch (error) {
                        reject(new ErrorHandler_1.FileOperationError('compress', filePath, error.message));
                    }
                });
                archive.on('error', (err) => {
                    reject(new ErrorHandler_1.FileOperationError('compress', filePath, err.message));
                });
                archive.pipe(output);
                if (isDirectory) {
                    archive.directory(filePath, false);
                }
                else {
                    archive.file(filePath, { name: path_1.default.basename(filePath) });
                }
                archive.finalize();
            });
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('compress', filePath, error.message);
        }
    }
    async extractFile(compressedPath, outputPath, options) {
        try {
            const finalOutputPath = outputPath || path_1.default.dirname(compressedPath);
            await fs_extra_1.default.ensureDir(finalOutputPath);
            const ext = path_1.default.extname(compressedPath).toLowerCase();
            let extractedFiles = [];
            let totalSize = 0;
            if (ext === '.zip') {
                return new Promise((resolve, reject) => {
                    fs_extra_1.default.createReadStream(compressedPath)
                        .pipe(unzipper_1.default.Extract({ path: finalOutputPath }))
                        .on('close', async () => {
                        try {
                            // Get list of extracted files
                            const files = await (0, fast_glob_1.default)('**/*', {
                                cwd: finalOutputPath,
                                absolute: true,
                                onlyFiles: true
                            });
                            for (const file of files) {
                                const stats = await fs_extra_1.default.stat(file);
                                totalSize += stats.size;
                                extractedFiles.push(file);
                            }
                            resolve({
                                success: true,
                                path: finalOutputPath,
                                operation: 'extract',
                                size: totalSize,
                                metadata: {
                                    compressedPath,
                                    extractedPath: finalOutputPath,
                                    extractedFiles,
                                    fileCount: extractedFiles.length,
                                    totalSize
                                }
                            });
                        }
                        catch (error) {
                            reject(new ErrorHandler_1.FileOperationError('extract', compressedPath, error.message));
                        }
                    })
                        .on('error', (err) => {
                        reject(new ErrorHandler_1.FileOperationError('extract', compressedPath, err.message));
                    });
                });
            }
            else {
                throw new Error(`Unsupported archive format: ${ext}`);
            }
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('extract', compressedPath, error.message);
        }
    }
    async syncDirectories(sourcePath, targetPath, options) {
        try {
            if (!await fs_extra_1.default.pathExists(sourcePath)) {
                throw new Error('Source directory does not exist');
            }
            await fs_extra_1.default.ensureDir(targetPath);
            // Copy all files from source to target
            await fs_extra_1.default.copy(sourcePath, targetPath, {
                overwrite: options.overwrite !== false,
                preserveTimestamps: options.preserveTimestamps !== false
            });
            const sourceStats = await fs_extra_1.default.stat(sourcePath);
            const targetStats = await fs_extra_1.default.stat(targetPath);
            return {
                success: true,
                path: targetPath,
                operation: 'sync',
                metadata: {
                    sourcePath,
                    targetPath,
                    sourceSize: sourceStats.size,
                    targetSize: targetStats.size,
                    syncedAt: new Date().toISOString()
                }
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('sync', sourcePath, error.message);
        }
    }
    async diffFiles(file1Path, file2Path, options) {
        try {
            const content1 = await fs_extra_1.default.readFile(file1Path, 'utf8');
            const content2 = await fs_extra_1.default.readFile(file2Path, 'utf8');
            const lines1 = content1.split('\n');
            const lines2 = content2.split('\n');
            const differences = [];
            const maxLines = Math.max(lines1.length, lines2.length);
            for (let i = 0; i < maxLines; i++) {
                const line1 = lines1[i];
                const line2 = lines2[i];
                if (line1 === undefined && line2 !== undefined) {
                    differences.push({ type: 'added', lineNumber: i + 1, content: line2 });
                }
                else if (line1 !== undefined && line2 === undefined) {
                    differences.push({ type: 'removed', lineNumber: i + 1, content: line1 });
                }
                else if (line1 !== line2) {
                    differences.push({ type: 'modified', lineNumber: i + 1, content: `- ${line1}\n+ ${line2}` });
                }
            }
            return {
                success: true,
                path: file1Path,
                operation: 'diff',
                metadata: {
                    file1: file1Path,
                    file2: file2Path,
                    differences,
                    differenceCount: differences.length,
                    identical: differences.length === 0
                }
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('diff', file1Path, error.message);
        }
    }
    async mergeFiles(filePaths, outputPath, options) {
        try {
            let mergedContent = '';
            const separator = options.separator || '\n';
            const processedFiles = [];
            for (const filePath of filePaths) {
                if (await fs_extra_1.default.pathExists(filePath)) {
                    const content = await fs_extra_1.default.readFile(filePath, 'utf8');
                    mergedContent += content + separator;
                    processedFiles.push(filePath);
                }
            }
            await fs_extra_1.default.writeFile(outputPath, mergedContent, 'utf8');
            const stats = await fs_extra_1.default.stat(outputPath);
            return {
                success: true,
                path: outputPath,
                operation: 'merge',
                size: stats.size,
                metadata: {
                    sourceFiles: processedFiles,
                    outputFile: outputPath,
                    fileCount: processedFiles.length,
                    separator
                }
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('merge', outputPath, error.message);
        }
    }
    async splitFile(filePath, chunkSize, options) {
        try {
            const content = await fs_extra_1.default.readFile(filePath, 'utf8');
            const chunks = [];
            const outputFiles = [];
            for (let i = 0; i < content.length; i += chunkSize) {
                chunks.push(content.slice(i, i + chunkSize));
            }
            const baseName = path_1.default.basename(filePath, path_1.default.extname(filePath));
            const extension = path_1.default.extname(filePath);
            const directory = path_1.default.dirname(filePath);
            for (let i = 0; i < chunks.length; i++) {
                const chunkFileName = `${baseName}.part${i + 1}${extension}`;
                const chunkFilePath = path_1.default.join(directory, chunkFileName);
                await fs_extra_1.default.writeFile(chunkFilePath, chunks[i], 'utf8');
                outputFiles.push(chunkFilePath);
            }
            return {
                success: true,
                path: filePath,
                operation: 'split',
                metadata: {
                    originalFile: filePath,
                    chunkSize,
                    chunkCount: chunks.length,
                    outputFiles
                }
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('split', filePath, error.message);
        }
    }
    async hashFile(filePath, algorithm = 'sha256', options) {
        try {
            const crypto = await Promise.resolve().then(() => __importStar(require('crypto')));
            const content = await fs_extra_1.default.readFile(filePath);
            const hash = crypto.createHash(algorithm).update(content).digest('hex');
            const stats = await fs_extra_1.default.stat(filePath);
            return {
                success: true,
                path: filePath,
                operation: 'hash',
                size: stats.size,
                metadata: {
                    algorithm,
                    hash,
                    fileSize: stats.size
                }
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('hash', filePath, error.message);
        }
    }
    async findFiles(criteria, searchPath, options) {
        try {
            const { name, extension, minSize, maxSize, modifiedAfter, modifiedBefore, content } = criteria;
            let pattern = '**/*';
            if (name)
                pattern = `**/*${name}*`;
            if (extension)
                pattern = `**/*.${extension}`;
            const files = await (0, fast_glob_1.default)(pattern, {
                cwd: searchPath,
                absolute: true,
                onlyFiles: true
            });
            const matchedFiles = [];
            for (const file of files) {
                const stats = await fs_extra_1.default.stat(file);
                let matches = true;
                const matchReasons = [];
                if (minSize && stats.size < minSize)
                    matches = false;
                if (maxSize && stats.size > maxSize)
                    matches = false;
                if (modifiedAfter && stats.mtime < new Date(modifiedAfter))
                    matches = false;
                if (modifiedBefore && stats.mtime > new Date(modifiedBefore))
                    matches = false;
                if (content) {
                    try {
                        const fileContent = await fs_extra_1.default.readFile(file, 'utf8');
                        if (fileContent.includes(content)) {
                            matchReasons.push('content match');
                        }
                        else {
                            matches = false;
                        }
                    }
                    catch {
                        matches = false;
                    }
                }
                if (matches) {
                    matchedFiles.push({
                        path: file,
                        size: stats.size,
                        modified: stats.mtime,
                        matches: matchReasons
                    });
                }
            }
            return {
                success: true,
                path: searchPath,
                operation: 'find',
                metadata: {
                    criteria,
                    matchedFiles,
                    matchCount: matchedFiles.length,
                    totalScanned: files.length
                }
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('find', searchPath, error.message);
        }
    }
    async replaceInFiles(pattern, replacement, searchPath, options) {
        try {
            const files = await (0, fast_glob_1.default)('**/*', {
                cwd: searchPath,
                absolute: true,
                onlyFiles: true
            });
            const modifiedFiles = [];
            let totalReplacements = 0;
            for (const file of files) {
                try {
                    const content = await fs_extra_1.default.readFile(file, 'utf8');
                    const regex = new RegExp(pattern, 'g');
                    const matches = content.match(regex);
                    if (matches && matches.length > 0) {
                        const newContent = content.replace(regex, replacement);
                        await fs_extra_1.default.writeFile(file, newContent, 'utf8');
                        modifiedFiles.push({
                            path: file,
                            replacements: matches.length
                        });
                        totalReplacements += matches.length;
                    }
                }
                catch {
                    // Skip files that can't be processed
                    continue;
                }
            }
            return {
                success: true,
                path: searchPath,
                operation: 'replace',
                metadata: {
                    pattern,
                    replacement,
                    modifiedFiles,
                    totalReplacements,
                    filesModified: modifiedFiles.length,
                    totalFilesScanned: files.length
                }
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('replace', searchPath, error.message);
        }
    }
    async batchOperation(operations, context, options) {
        try {
            const results = [];
            const errors = [];
            for (const operation of operations) {
                try {
                    const result = await this.execute(operation, context);
                    results.push({
                        operation: operation.operation,
                        path: operation.path,
                        success: result.success,
                        result: result.result
                    });
                }
                catch (error) {
                    errors.push({
                        operation: operation.operation,
                        path: operation.path,
                        error: error.message
                    });
                }
            }
            const successCount = results.filter(r => r.success).length;
            const failureCount = errors.length + results.filter(r => !r.success).length;
            return {
                success: errors.length === 0,
                path: context.workingDirectory,
                operation: 'batch',
                metadata: {
                    totalOperations: operations.length,
                    successCount,
                    failureCount,
                    results,
                    errors
                }
            };
        }
        catch (error) {
            throw new ErrorHandler_1.FileOperationError('batch', context.workingDirectory, error.message);
        }
    }
}
exports.FileTool = FileTool;
//# sourceMappingURL=FileTool.js.map