"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OllamaProvider = void 0;
const node_fetch_1 = __importDefault(require("node-fetch"));
const ErrorHandler_1 = require("../../utils/ErrorHandler");
const Logger_1 = require("../../utils/Logger");
const ToolRegistry_1 = require("../../tools/ToolRegistry");
class OllamaProvider {
    name = 'ollama';
    baseURL;
    timeout;
    logger;
    toolRegistry;
    constructor(config) {
        this.logger = Logger_1.Logger.getInstance();
        this.toolRegistry = ToolRegistry_1.ToolRegistry.getInstance();
        this.baseURL = config.baseURL || 'http://localhost:11434';
        this.timeout = config.timeout || 30000;
    }
    async generateResponse(prompt, options = {}) {
        try {
            const model = options.model || 'llama3';
            const messages = this.buildMessages(prompt, options);
            const requestBody = {
                model,
                messages,
                stream: false,
                options: {
                    temperature: options.temperature || 0.7,
                    num_predict: options.maxTokens || 4000
                }
            };
            // Add tool descriptions to system prompt if tools are available
            if (options.tools && options.tools.length > 0) {
                const toolDescriptions = this.buildToolDescriptions(options.tools);
                const systemMessage = messages.find(m => m.role === 'system');
                if (systemMessage) {
                    systemMessage.content += `\n\n${toolDescriptions}`;
                }
                else {
                    messages.unshift({
                        role: 'system',
                        content: toolDescriptions
                    });
                }
            }
            this.logger.debug('Making Ollama API request', {
                model,
                messageCount: messages.length,
                baseURL: this.baseURL
            });
            const response = await (0, node_fetch_1.default)(`${this.baseURL}/api/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody),
                signal: AbortSignal.timeout(this.timeout)
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const data = await response.json();
            const content = data.message?.content || '';
            // Parse tool calls from response if tools were provided
            const toolCalls = options.tools && options.tools.length > 0 ?
                this.parseToolCalls(content) : [];
            const llmResponse = {
                content,
                toolCalls,
                usage: {
                    promptTokens: data.prompt_eval_count || 0,
                    completionTokens: data.eval_count || 0,
                    totalTokens: (data.prompt_eval_count || 0) + (data.eval_count || 0)
                }
            };
            this.logger.logLLMCall(this.name, model, prompt, content, llmResponse.usage);
            return llmResponse;
        }
        catch (error) {
            this.logger.error('Ollama API request failed', {
                error: error.message,
                prompt: `${prompt.substring(0, 100)}...`,
                baseURL: this.baseURL
            });
            throw new ErrorHandler_1.LLMProviderError(this.name, error.message);
        }
    }
    async *generateStreamResponse(prompt, options = {}) {
        try {
            const model = options.model || 'llama3';
            const messages = this.buildMessages(prompt, options);
            const requestBody = {
                model,
                messages,
                stream: true,
                options: {
                    temperature: options.temperature || 0.7,
                    num_predict: options.maxTokens || 4000
                }
            };
            this.logger.debug('Making Ollama streaming API request', {
                model,
                messageCount: messages.length,
                baseURL: this.baseURL
            });
            const response = await (0, node_fetch_1.default)(`${this.baseURL}/api/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody),
                signal: AbortSignal.timeout(this.timeout)
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const reader = response.body?.getReader();
            if (!reader) {
                throw new Error('No response body reader available');
            }
            const decoder = new TextDecoder();
            let buffer = '';
            while (true) {
                const { done, value } = await reader.read();
                if (done)
                    break;
                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';
                for (const line of lines) {
                    if (line.trim()) {
                        try {
                            const data = JSON.parse(line);
                            if (data.message?.content) {
                                yield data.message.content;
                            }
                        }
                        catch {
                            // Skip invalid JSON lines
                        }
                    }
                }
            }
        }
        catch (error) {
            this.logger.error('Ollama streaming API request failed', {
                error: error.message,
                prompt: `${prompt.substring(0, 100)}...`,
                baseURL: this.baseURL
            });
            throw new ErrorHandler_1.LLMProviderError(this.name, error.message);
        }
    }
    supportsToolCalling() {
        return true; // Now supports tool calling through function descriptions
    }
    async callTool(toolCall, context) {
        try {
            return await this.toolRegistry.executeTool(toolCall, context);
        }
        catch (error) {
            this.logger.error('Tool execution failed in Ollama provider', {
                toolName: toolCall.name,
                error: error.message
            });
            throw error;
        }
    }
    buildMessages(prompt, options) {
        const messages = [];
        // Add system message if provided
        if (options.systemPrompt) {
            messages.push({
                role: 'system',
                content: options.systemPrompt
            });
        }
        // Add conversation history if provided
        if (options.conversationHistory) {
            for (const msg of options.conversationHistory) {
                if (msg.role !== 'tool') { // Skip tool messages for now
                    messages.push({
                        role: msg.role,
                        content: msg.content
                    });
                }
            }
        }
        // Add current prompt
        messages.push({
            role: 'user',
            content: prompt
        });
        return messages;
    }
    async validateConnection() {
        try {
            const response = await (0, node_fetch_1.default)(`${this.baseURL}/api/tags`, {
                signal: AbortSignal.timeout(5000)
            });
            return response.ok;
        }
        catch (error) {
            this.logger.error('Ollama connection validation failed', {
                error: error.message,
                baseURL: this.baseURL
            });
            return false;
        }
    }
    async getAvailableModels() {
        try {
            const response = await (0, node_fetch_1.default)(`${this.baseURL}/api/tags`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const data = await response.json();
            return data.models?.map((model) => model.name) || [];
        }
        catch (error) {
            this.logger.error('Failed to fetch Ollama models', {
                error: error.message,
                baseURL: this.baseURL
            });
            return ['llama3', 'llama2', 'codellama', 'mistral'];
        }
    }
    getDefaultModel() {
        return 'llama3';
    }
    getMaxTokens(_model) {
        // Most Ollama models support around 32k tokens
        return 32000;
    }
    estimateTokens(text) {
        // Rough estimation: 1 token ≈ 4 characters for English text
        return Math.ceil(text.length / 4);
    }
    calculateCost(_usage, _model) {
        // Ollama is free (local execution)
        return 0;
    }
    buildToolDescriptions(tools) {
        const descriptions = tools.map(tool => {
            const params = tool.parameters.properties ?
                Object.entries(tool.parameters.properties)
                    .map(([name, prop]) => `${name}: ${prop.description}`)
                    .join(', ') : '';
            return `${tool.name}(${params}): ${tool.description}`;
        });
        return `Available tools:
${descriptions.join('\n')}

To use a tool, respond with a JSON object in this format:
{"tool_call": {"name": "tool_name", "arguments": {"param1": "value1", "param2": "value2"}}}

You can call multiple tools by providing an array:
[{"tool_call": {"name": "tool1", "arguments": {...}}}, {"tool_call": {"name": "tool2", "arguments": {...}}}]`;
    }
    parseToolCalls(content) {
        const toolCalls = [];
        try {
            // Look for JSON objects with tool_call structure
            const jsonRegex = /\{[^{}]*"tool_call"[^{}]*\}/g;
            const arrayRegex = /\[[^\[\]]*"tool_call"[^\[\]]*\]/g;
            let matches = content.match(arrayRegex);
            if (matches) {
                // Handle array format
                for (const match of matches) {
                    try {
                        const parsed = JSON.parse(match);
                        if (Array.isArray(parsed)) {
                            for (const item of parsed) {
                                if (item.tool_call) {
                                    toolCalls.push({
                                        id: `ollama_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                                        name: item.tool_call.name,
                                        arguments: item.tool_call.arguments || {}
                                    });
                                }
                            }
                        }
                    }
                    catch {
                        // Skip invalid JSON
                    }
                }
            }
            else {
                // Handle single object format
                matches = content.match(jsonRegex);
                if (matches) {
                    for (const match of matches) {
                        try {
                            const parsed = JSON.parse(match);
                            if (parsed.tool_call) {
                                toolCalls.push({
                                    id: `ollama_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                                    name: parsed.tool_call.name,
                                    arguments: parsed.tool_call.arguments || {}
                                });
                            }
                        }
                        catch {
                            // Skip invalid JSON
                        }
                    }
                }
            }
        }
        catch (error) {
            this.logger.debug('Failed to parse tool calls from Ollama response', {
                error: error.message,
                content: content.substring(0, 200)
            });
        }
        return toolCalls;
    }
}
exports.OllamaProvider = OllamaProvider;
//# sourceMappingURL=OllamaProvider.js.map