"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnthropicProvider = void 0;
const sdk_1 = __importDefault(require("@anthropic-ai/sdk"));
const ErrorHandler_1 = require("../../utils/ErrorHandler");
const Logger_1 = require("../../utils/Logger");
const ToolRegistry_1 = require("../../tools/ToolRegistry");
class AnthropicProvider {
    name = 'anthropic';
    client;
    logger;
    toolRegistry;
    constructor(config) {
        this.logger = Logger_1.Logger.getInstance();
        this.toolRegistry = ToolRegistry_1.ToolRegistry.getInstance();
        if (!config.apiKey) {
            throw new ErrorHandler_1.LLMProviderError(this.name, 'Anthropic API key is required');
        }
        this.client = new sdk_1.default({
            apiKey: config.apiKey,
            baseURL: config.baseURL
        });
    }
    async generateResponse(prompt, options = {}) {
        try {
            const model = options.model || 'claude-3-5-sonnet-20241022';
            const messages = this.buildMessages(prompt, options);
            const requestParams = {
                model,
                messages,
                max_tokens: options.maxTokens || 4000,
                temperature: options.temperature || 0.7
            };
            // Add system message if provided
            if (options.systemPrompt) {
                requestParams.system = options.systemPrompt;
            }
            // Add tools if available and enabled
            if (options.tools && options.tools.length > 0) {
                requestParams.tools = options.tools.map(tool => ({
                    name: tool.name,
                    description: tool.description,
                    input_schema: tool.parameters
                }));
            }
            this.logger.debug('Making Anthropic API request', {
                model,
                messageCount: messages.length,
                hasTools: !!requestParams.tools,
                toolCount: requestParams.tools?.length || 0
            });
            const response = await this.client.messages.create(requestParams);
            let content = '';
            const toolCalls = [];
            for (const block of response.content) {
                if (block.type === 'text') {
                    content += block.text;
                }
                else if (block.type === 'tool_use') {
                    toolCalls.push({
                        id: block.id,
                        name: block.name,
                        arguments: block.input
                    });
                }
            }
            const llmResponse = {
                content,
                toolCalls,
                usage: response.usage ? {
                    promptTokens: response.usage.input_tokens,
                    completionTokens: response.usage.output_tokens,
                    totalTokens: response.usage.input_tokens + response.usage.output_tokens
                } : undefined
            };
            this.logger.logLLMCall(this.name, model, prompt, content, llmResponse.usage);
            return llmResponse;
        }
        catch (error) {
            this.logger.error('Anthropic API request failed', {
                error: error.message,
                prompt: `${prompt.substring(0, 100)}...`
            });
            if (error instanceof sdk_1.default.APIError) {
                throw new ErrorHandler_1.LLMProviderError(this.name, `Anthropic API error: ${error.message}`, { status: error.status, type: error.type });
            }
            throw new ErrorHandler_1.LLMProviderError(this.name, error.message);
        }
    }
    async *generateStreamResponse(prompt, options = {}) {
        try {
            const model = options.model || 'claude-3-5-sonnet-20241022';
            const messages = this.buildMessages(prompt, options);
            const requestParams = {
                model,
                messages,
                max_tokens: options.maxTokens || 4000,
                temperature: options.temperature || 0.7,
                stream: true
            };
            // Add system message if provided
            if (options.systemPrompt) {
                requestParams.system = options.systemPrompt;
            }
            // Add tools if available and enabled
            if (options.tools && options.tools.length > 0) {
                requestParams.tools = options.tools.map(tool => ({
                    name: tool.name,
                    description: tool.description,
                    input_schema: tool.parameters
                }));
            }
            this.logger.debug('Making Anthropic streaming API request', {
                model,
                messageCount: messages.length,
                hasTools: !!requestParams.tools
            });
            const stream = await this.client.messages.create(requestParams);
            for await (const event of stream) {
                if (event.type === 'content_block_delta' && event.delta.type === 'text_delta') {
                    yield event.delta.text;
                }
            }
        }
        catch (error) {
            this.logger.error('Anthropic streaming API request failed', {
                error: error.message,
                prompt: `${prompt.substring(0, 100)}...`
            });
            if (error instanceof sdk_1.default.APIError) {
                throw new ErrorHandler_1.LLMProviderError(this.name, `Anthropic streaming API error: ${error.message}`, { status: error.status, type: error.type });
            }
            throw new ErrorHandler_1.LLMProviderError(this.name, error.message);
        }
    }
    supportsToolCalling() {
        return true;
    }
    async callTool(toolCall, context) {
        try {
            return await this.toolRegistry.executeTool(toolCall, context);
        }
        catch (error) {
            this.logger.error('Tool execution failed in Anthropic provider', {
                toolName: toolCall.name,
                error: error.message
            });
            throw error;
        }
    }
    buildMessages(prompt, options) {
        const messages = [];
        // Add conversation history if provided (excluding system messages)
        if (options.conversationHistory) {
            for (const msg of options.conversationHistory) {
                if (msg.role === 'system') {
                    // System messages are handled separately in Anthropic
                    continue;
                }
                if (msg.role === 'tool') {
                    // Tool results need to be formatted differently
                    messages.push({
                        role: 'user',
                        content: [
                            {
                                type: 'tool_result',
                                tool_use_id: msg.toolCallId,
                                content: msg.content
                            }
                        ]
                    });
                }
                else {
                    messages.push({
                        role: msg.role,
                        content: msg.content
                    });
                }
            }
        }
        // Add current prompt
        messages.push({
            role: 'user',
            content: prompt
        });
        return messages;
    }
    async validateApiKey() {
        try {
            // Test with a minimal request
            await this.client.messages.create({
                model: 'claude-3-haiku-20240307',
                messages: [{ role: 'user', content: 'Hi' }],
                max_tokens: 10
            });
            return true;
        }
        catch (error) {
            this.logger.error('Anthropic API key validation failed', {
                error: error.message
            });
            return false;
        }
    }
    async getAvailableModels() {
        // Anthropic doesn't have a models endpoint, so return known models
        return [
            'claude-3-opus-20240229',
            'claude-3-sonnet-20240229',
            'claude-3-haiku-20240307',
            'claude-3-5-sonnet-20241022'
        ];
    }
    getDefaultModel() {
        return 'claude-3-5-sonnet-20241022';
    }
    getMaxTokens(model) {
        const tokenLimits = {
            'claude-3-opus-20240229': 200000,
            'claude-3-sonnet-20240229': 200000,
            'claude-3-haiku-20240307': 200000,
            'claude-3-5-sonnet-20241022': 200000
        };
        return tokenLimits[model || 'claude-3-5-sonnet-20241022'] || 200000;
    }
    estimateTokens(text) {
        // Anthropic's tokenization is roughly 1 token per 3.5 characters
        return Math.ceil(text.length / 3.5);
    }
    calculateCost(usage, model) {
        // Pricing as of 2024 (in USD per 1M tokens)
        const pricing = {
            'claude-3-opus-20240229': { input: 15, output: 75 },
            'claude-3-sonnet-20240229': { input: 3, output: 15 },
            'claude-3-haiku-20240307': { input: 0.25, output: 1.25 },
            'claude-3-5-sonnet-20241022': { input: 3, output: 15 }
        };
        const modelPricing = pricing[model || 'claude-3-5-sonnet-20241022'] || pricing['claude-3-5-sonnet-20241022'];
        const inputCost = (usage.promptTokens / 1000000) * modelPricing.input;
        const outputCost = (usage.completionTokens / 1000000) * modelPricing.output;
        return inputCost + outputCost;
    }
}
exports.AnthropicProvider = AnthropicProvider;
//# sourceMappingURL=AnthropicProvider.js.map