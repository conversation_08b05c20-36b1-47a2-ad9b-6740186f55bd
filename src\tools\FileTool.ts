import fs from 'fs-extra';
import path from 'path';
import glob from 'fast-glob';
import archiver from 'archiver';
import unzipper from 'unzipper';
import { Tool, ToolResult, AgentContext, FileOperationOptions, FileOperationResult, SearchOptions, SearchResult, BufferEncoding } from '../types';
import { FileOperationError } from '../utils/ErrorHandler';
import { Logger } from '../utils/Logger';

export class FileTool implements Tool {
  public readonly name = 'file';
  public readonly description = 'Comprehensive file operations including read, write, search, and manipulation';
  public readonly parameters = {
    type: 'object' as const,
    properties: {
      operation: {
        type: 'string',
        description: 'File operation to perform',
        enum: ['read', 'write', 'append', 'delete', 'copy', 'move', 'mkdir', 'rmdir', 'exists', 'stat', 'search', 'glob', 'permissions', 'watch', 'backup', 'restore', 'compress', 'extract', 'sync']
      },
      path: {
        type: 'string',
        description: 'File or directory path'
      },
      content: {
        type: 'string',
        description: 'Content to write (for write/append operations)'
      },
      destination: {
        type: 'string',
        description: 'Destination path (for copy/move operations)'
      },
      pattern: {
        type: 'string',
        description: 'Search pattern or glob pattern'
      },
      options: {
        type: 'object',
        description: 'Additional options for the operation'
      }
    },
    required: ['operation', 'path']
  };

  private logger: Logger;

  constructor() {
    this.logger = Logger.getInstance();
  }

  public async execute(args: Record<string, unknown>, context: AgentContext): Promise<ToolResult> {
    const startTime = Date.now();

    // Type validation and conversion
    const operation = String(args.operation);
    const filePath = String(args.path);
    const content = args.content ? String(args.content) : '';
    const destination = args.destination ? String(args.destination) : '';
    const pattern = args.pattern ? String(args.pattern) : '';
    const chunkSize = args.chunkSize ? Number(args.chunkSize) : 1024;
    const algorithm = args.algorithm ? String(args.algorithm) : 'sha256';
    const files = Array.isArray(args.files) ? args.files.map(f => String(f)) : [];
    const replacement = args.replacement ? String(args.replacement) : '';
    const operations = Array.isArray(args.operations) ? args.operations : [];
    const criteria = (args.criteria as Record<string, unknown>) || {};

    try {
      const absolutePath = path.resolve(context.workingDirectory, filePath);
      const options: FileOperationOptions = (args.options as FileOperationOptions) || {};

      let result: FileOperationResult;

      switch (operation) {
        case 'read':
          result = await this.readFile(absolutePath, options);
          break;
        case 'write':
          result = await this.writeFile(absolutePath, content, options);
          break;
        case 'append':
          result = await this.appendFile(absolutePath, content, options);
          break;
        case 'delete':
          result = await this.deleteFile(absolutePath, options);
          break;
        case 'copy':
          result = await this.copyFile(absolutePath, destination, options);
          break;
        case 'move':
          result = await this.moveFile(absolutePath, destination, options);
          break;
        case 'mkdir':
          result = await this.createDirectory(absolutePath, options);
          break;
        case 'rmdir':
          result = await this.removeDirectory(absolutePath, options);
          break;
        case 'exists':
          result = await this.checkExists(absolutePath);
          break;
        case 'stat':
          result = await this.getStats(absolutePath);
          break;
        case 'search':
          const searchOptions: SearchOptions = { pattern, ...options };
          const searchResults = await this.searchInFiles(absolutePath, searchOptions);
          result = {
            success: true,
            path: absolutePath,
            operation: 'search',
            metadata: { results: searchResults, count: searchResults.length }
          };
          break;
        case 'glob':
          const globResults = await this.globFiles(pattern, context.workingDirectory);
          result = {
            success: true,
            path: context.workingDirectory,
            operation: 'glob',
            metadata: { files: globResults, count: globResults.length }
          };
          break;
        case 'permissions':
          result = await this.getPermissions(absolutePath);
          break;
        case 'watch':
          result = await this.watchFile(absolutePath, options);
          break;
        case 'backup':
          result = await this.backupFile(absolutePath, destination, options);
          break;
        case 'restore':
          result = await this.restoreFile(absolutePath, destination, options);
          break;
        case 'compress':
          result = await this.compressFile(absolutePath, destination, options);
          break;
        case 'extract':
          result = await this.extractFile(absolutePath, destination, options);
          break;
        case 'sync':
          result = await this.syncDirectories(absolutePath, destination, options);
          break;
        case 'diff':
          result = await this.diffFiles(absolutePath, destination, options);
          break;
        case 'merge':
          result = await this.mergeFiles(files, absolutePath, options);
          break;
        case 'split':
          result = await this.splitFile(absolutePath, chunkSize, options);
          break;
        case 'hash':
          result = await this.hashFile(absolutePath, algorithm, options);
          break;
        case 'find':
          result = await this.findFiles(criteria, absolutePath, options);
          break;
        case 'replace':
          result = await this.replaceInFiles(pattern, replacement, absolutePath, options);
          break;
        case 'batch':
          result = await this.batchOperation(operations, context, options);
          break;
        default:
          throw new FileOperationError(operation, absolutePath, 'Unknown operation');
      }

      const duration = Date.now() - startTime;
      this.logger.logToolExecution(this.name, args, result, duration);

      return {
        success: result.success,
        result,
        metadata: {
          operation: args.operation,
          path: absolutePath,
          duration
        }
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`File operation failed: ${args.operation} on ${args.path}`, {
        error: (error as Error).message,
        operation: args.operation,
        path: args.path,
        duration
      });

      if (error instanceof FileOperationError) {
        throw error;
      }

      throw new FileOperationError(
        operation,
        filePath,
        (error as Error).message,
        { duration }
      );
    }
  }

  private async readFile(filePath: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      const content = await fs.readFile(filePath, (options.encoding || 'utf8') as BufferEncoding);
      const stats = await fs.stat(filePath);
      
      return {
        success: true,
        path: filePath,
        operation: 'read',
        size: stats.size,
        metadata: { content, encoding: options.encoding || 'utf8' }
      };
    } catch (error) {
      throw new FileOperationError('read', filePath, (error as Error).message);
    }
  }

  private async writeFile(filePath: string, content: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      if (options.createDirectories) {
        await fs.ensureDir(path.dirname(filePath));
      }

      if (!options.overwrite && await fs.pathExists(filePath)) {
        throw new Error('File exists and overwrite is disabled');
      }

      await fs.writeFile(filePath, content, (options.encoding || 'utf8') as BufferEncoding);
      const stats = await fs.stat(filePath);

      return {
        success: true,
        path: filePath,
        operation: 'write',
        size: stats.size
      };
    } catch (error) {
      throw new FileOperationError('write', filePath, (error as Error).message);
    }
  }

  private async appendFile(filePath: string, content: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      if (options.createDirectories) {
        await fs.ensureDir(path.dirname(filePath));
      }

      await fs.appendFile(filePath, content, (options.encoding || 'utf8') as BufferEncoding);
      const stats = await fs.stat(filePath);

      return {
        success: true,
        path: filePath,
        operation: 'append',
        size: stats.size
      };
    } catch (error) {
      throw new FileOperationError('append', filePath, (error as Error).message);
    }
  }

  private async deleteFile(filePath: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      if (options.force) {
        await fs.remove(filePath);
      } else {
        await fs.unlink(filePath);
      }

      return {
        success: true,
        path: filePath,
        operation: 'delete'
      };
    } catch (error) {
      throw new FileOperationError('delete', filePath, (error as Error).message);
    }
  }

  private async copyFile(sourcePath: string, destPath: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      const absoluteDestPath = path.resolve(path.dirname(sourcePath), destPath);
      
      if (options.createDirectories) {
        await fs.ensureDir(path.dirname(absoluteDestPath));
      }

      const copyOptions: any = {};
      if (options.overwrite !== undefined) {
        copyOptions.overwrite = options.overwrite;
      }
      if (options.preserveTimestamps) {
        copyOptions.preserveTimestamps = true;
      }

      await fs.copy(sourcePath, absoluteDestPath, copyOptions);
      const stats = await fs.stat(absoluteDestPath);

      return {
        success: true,
        path: absoluteDestPath,
        operation: 'copy',
        size: stats.size,
        metadata: { source: sourcePath, destination: absoluteDestPath }
      };
    } catch (error) {
      throw new FileOperationError('copy', sourcePath, (error as Error).message);
    }
  }

  private async moveFile(sourcePath: string, destPath: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      const absoluteDestPath = path.resolve(path.dirname(sourcePath), destPath);
      
      if (options.createDirectories) {
        await fs.ensureDir(path.dirname(absoluteDestPath));
      }

      await fs.move(sourcePath, absoluteDestPath, { overwrite: options.overwrite });
      const stats = await fs.stat(absoluteDestPath);

      return {
        success: true,
        path: absoluteDestPath,
        operation: 'move',
        size: stats.size,
        metadata: { source: sourcePath, destination: absoluteDestPath }
      };
    } catch (error) {
      throw new FileOperationError('move', sourcePath, (error as Error).message);
    }
  }

  private async createDirectory(dirPath: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      if (options.recursive !== false) {
        await fs.ensureDir(dirPath);
      } else {
        await fs.mkdir(dirPath);
      }

      return {
        success: true,
        path: dirPath,
        operation: 'mkdir'
      };
    } catch (error) {
      throw new FileOperationError('mkdir', dirPath, (error as Error).message);
    }
  }

  private async removeDirectory(dirPath: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      if (options.recursive !== false) {
        await fs.remove(dirPath);
      } else {
        await fs.rmdir(dirPath);
      }

      return {
        success: true,
        path: dirPath,
        operation: 'rmdir'
      };
    } catch (error) {
      throw new FileOperationError('rmdir', dirPath, (error as Error).message);
    }
  }

  private async checkExists(filePath: string): Promise<FileOperationResult> {
    try {
      const exists = await fs.pathExists(filePath);
      
      return {
        success: true,
        path: filePath,
        operation: 'exists',
        metadata: { exists }
      };
    } catch (error) {
      throw new FileOperationError('exists', filePath, (error as Error).message);
    }
  }

  private async getStats(filePath: string): Promise<FileOperationResult> {
    try {
      const stats = await fs.stat(filePath);
      
      return {
        success: true,
        path: filePath,
        operation: 'stat',
        size: stats.size,
        metadata: {
          isFile: stats.isFile(),
          isDirectory: stats.isDirectory(),
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime,
          accessed: stats.atime,
          mode: stats.mode,
          uid: stats.uid,
          gid: stats.gid
        }
      };
    } catch (error) {
      throw new FileOperationError('stat', filePath, (error as Error).message);
    }
  }

  private async getPermissions(filePath: string): Promise<FileOperationResult> {
    try {
      const stats = await fs.stat(filePath);
      const mode = stats.mode;
      const permissions = {
        owner: {
          read: !!(mode & parseInt('400', 8)),
          write: !!(mode & parseInt('200', 8)),
          execute: !!(mode & parseInt('100', 8))
        },
        group: {
          read: !!(mode & parseInt('040', 8)),
          write: !!(mode & parseInt('020', 8)),
          execute: !!(mode & parseInt('010', 8))
        },
        others: {
          read: !!(mode & parseInt('004', 8)),
          write: !!(mode & parseInt('002', 8)),
          execute: !!(mode & parseInt('001', 8))
        }
      };

      return {
        success: true,
        path: filePath,
        operation: 'permissions',
        metadata: { permissions, mode: mode.toString(8) }
      };
    } catch (error) {
      throw new FileOperationError('permissions', filePath, (error as Error).message);
    }
  }

  private async searchInFiles(searchPath: string, options: SearchOptions): Promise<SearchResult[]> {
    const results: SearchResult[] = [];
    const files = await glob(options.includeFiles || ['**/*'], {
      cwd: searchPath,
      ignore: options.excludeFiles || [],
      onlyFiles: true,
      absolute: true
    });

    for (const file of files) {
      try {
        const content = await fs.readFile(file, 'utf8');
        const lines = content.split('\n');
        
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i];
          if (!line) continue;

          let match: RegExpMatchArray | null = null;

          if (options.regex) {
            const flags = options.caseSensitive ? 'g' : 'gi';
            const regex = new RegExp(options.pattern, flags);
            match = line.match(regex);
          } else {
            const searchText = options.caseSensitive ? line : line.toLowerCase();
            const pattern = options.caseSensitive ? options.pattern : options.pattern.toLowerCase();

            if (options.wholeWord) {
              const regex = new RegExp(`\\b${pattern}\\b`, options.caseSensitive ? 'g' : 'gi');
              match = line.match(regex);
            } else {
              if (searchText && searchText.includes(pattern)) {
                match = [pattern];
              }
            }
          }

          if (match) {
            const contextStart = Math.max(0, i - (options.context || 0));
            const contextEnd = Math.min(lines.length - 1, i + (options.context || 0));
            const context = lines.slice(contextStart, contextEnd + 1);

            results.push({
              file,
              line: i + 1,
              column: line.indexOf(match[0]) + 1,
              match: match[0],
              context
            });

            if (options.maxResults && results.length >= options.maxResults) {
              return results;
            }
          }
        }
      } catch (_error) {
        // Skip files that can't be read
        continue;
      }
    }

    return results;
  }

  private async globFiles(pattern: string, cwd: string): Promise<string[]> {
    try {
      return await glob(pattern, { cwd, absolute: true });
    } catch (error) {
      throw new Error(`Glob pattern failed: ${(error as Error).message}`);
    }
  }

  private async watchFile(filePath: string, _options?: FileOperationOptions): Promise<FileOperationResult> {
    try {
      // This is a simplified implementation - in a real scenario, you'd set up file watchers
      const stats = await fs.stat(filePath);

      return {
        success: true,
        path: filePath,
        operation: 'watch',
        metadata: {
          message: 'File watching initiated (simplified implementation)',
          lastModified: stats.mtime,
          size: stats.size
        }
      };
    } catch (error) {
      throw new FileOperationError('watch', filePath, (error as Error).message);
    }
  }

  private async backupFile(filePath: string, backupPath: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const finalBackupPath = backupPath || `${filePath}.backup.${timestamp}`;

      await fs.copy(filePath, finalBackupPath, {
        preserveTimestamps: options.preserveTimestamps !== false
      });

      const stats = await fs.stat(finalBackupPath);

      return {
        success: true,
        path: finalBackupPath,
        operation: 'backup',
        size: stats.size,
        metadata: {
          originalPath: filePath,
          backupPath: finalBackupPath,
          timestamp
        }
      };
    } catch (error) {
      throw new FileOperationError('backup', filePath, (error as Error).message);
    }
  }

  private async restoreFile(backupPath: string, originalPath: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      if (!await fs.pathExists(backupPath)) {
        throw new Error('Backup file does not exist');
      }

      if (!options.overwrite && await fs.pathExists(originalPath)) {
        throw new Error('Target file exists and overwrite is disabled');
      }

      await fs.copy(backupPath, originalPath, {
        preserveTimestamps: options.preserveTimestamps !== false
      });

      const stats = await fs.stat(originalPath);

      return {
        success: true,
        path: originalPath,
        operation: 'restore',
        size: stats.size,
        metadata: {
          backupPath,
          originalPath
        }
      };
    } catch (error) {
      throw new FileOperationError('restore', backupPath, (error as Error).message);
    }
  }

  private async compressFile(filePath: string, outputPath: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      const stats = await fs.stat(filePath);
      const isDirectory = stats.isDirectory();
      const format = options.format || 'zip';
      const finalOutputPath = outputPath || `${filePath}.${format}`;

      return new Promise((resolve, reject) => {
        const output = fs.createWriteStream(finalOutputPath);
        const archive = archiver(format as 'zip' | 'tar', {
          zlib: { level: options.compressionLevel || 9 }
        });

        output.on('close', async () => {
          try {
            const compressedStats = await fs.stat(finalOutputPath);
            resolve({
              success: true,
              path: finalOutputPath,
              operation: 'compress',
              size: compressedStats.size,
              metadata: {
                originalPath: filePath,
                compressedPath: finalOutputPath,
                originalSize: stats.size,
                compressedSize: compressedStats.size,
                compressionRatio: compressedStats.size / stats.size,
                format,
                totalBytes: archive.pointer()
              }
            });
          } catch (error) {
            reject(new FileOperationError('compress', filePath, (error as Error).message));
          }
        });

        archive.on('error', (err) => {
          reject(new FileOperationError('compress', filePath, err.message));
        });

        archive.pipe(output);

        if (isDirectory) {
          archive.directory(filePath, false);
        } else {
          archive.file(filePath, { name: path.basename(filePath) });
        }

        archive.finalize();
      });
    } catch (error) {
      throw new FileOperationError('compress', filePath, (error as Error).message);
    }
  }

  private async extractFile(compressedPath: string, outputPath: string, _options?: FileOperationOptions): Promise<FileOperationResult> {
    try {
      const finalOutputPath = outputPath || path.dirname(compressedPath);
      await fs.ensureDir(finalOutputPath);

      const ext = path.extname(compressedPath).toLowerCase();
      const extractedFiles: string[] = [];
      let totalSize = 0;

      if (ext === '.zip') {
        return new Promise((resolve, reject) => {
          fs.createReadStream(compressedPath)
            .pipe(unzipper.Extract({ path: finalOutputPath }))
            .on('close', async () => {
              try {
                // Get list of extracted files
                const files = await glob('**/*', {
                  cwd: finalOutputPath,
                  absolute: true,
                  onlyFiles: true
                });

                for (const file of files) {
                  const stats = await fs.stat(file);
                  totalSize += stats.size;
                  extractedFiles.push(file);
                }

                resolve({
                  success: true,
                  path: finalOutputPath,
                  operation: 'extract',
                  size: totalSize,
                  metadata: {
                    compressedPath,
                    extractedPath: finalOutputPath,
                    extractedFiles,
                    fileCount: extractedFiles.length,
                    totalSize
                  }
                });
              } catch (error) {
                reject(new FileOperationError('extract', compressedPath, (error as Error).message));
              }
            })
            .on('error', (err: Error) => {
              reject(new FileOperationError('extract', compressedPath, err.message));
            });
        });
      } else {
        throw new Error(`Unsupported archive format: ${ext}`);
      }
    } catch (error) {
      throw new FileOperationError('extract', compressedPath, (error as Error).message);
    }
  }

  private async syncDirectories(sourcePath: string, targetPath: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      if (!await fs.pathExists(sourcePath)) {
        throw new Error('Source directory does not exist');
      }

      await fs.ensureDir(targetPath);

      // Copy all files from source to target
      await fs.copy(sourcePath, targetPath, {
        overwrite: options.overwrite !== false,
        preserveTimestamps: options.preserveTimestamps !== false
      });

      const sourceStats = await fs.stat(sourcePath);
      const targetStats = await fs.stat(targetPath);

      return {
        success: true,
        path: targetPath,
        operation: 'sync',
        metadata: {
          sourcePath,
          targetPath,
          sourceSize: sourceStats.size,
          targetSize: targetStats.size,
          syncedAt: new Date().toISOString()
        }
      };
    } catch (error) {
      throw new FileOperationError('sync', sourcePath, (error as Error).message);
    }
  }

  private async diffFiles(file1Path: string, file2Path: string, _options?: FileOperationOptions): Promise<FileOperationResult> {
    try {
      const content1 = await fs.readFile(file1Path, 'utf8');
      const content2 = await fs.readFile(file2Path, 'utf8');

      const lines1 = content1.split('\n');
      const lines2 = content2.split('\n');

      const differences: Array<{
        type: 'added' | 'removed' | 'modified';
        lineNumber: number;
        content: string;
      }> = [];

      const maxLines = Math.max(lines1.length, lines2.length);

      for (let i = 0; i < maxLines; i++) {
        const line1 = lines1[i];
        const line2 = lines2[i];

        if (line1 === undefined && line2 !== undefined) {
          differences.push({ type: 'added', lineNumber: i + 1, content: line2 });
        } else if (line1 !== undefined && line2 === undefined) {
          differences.push({ type: 'removed', lineNumber: i + 1, content: line1 });
        } else if (line1 !== line2) {
          differences.push({ type: 'modified', lineNumber: i + 1, content: `- ${line1}\n+ ${line2}` });
        }
      }

      return {
        success: true,
        path: file1Path,
        operation: 'diff',
        metadata: {
          file1: file1Path,
          file2: file2Path,
          differences,
          differenceCount: differences.length,
          identical: differences.length === 0
        }
      };
    } catch (error) {
      throw new FileOperationError('diff', file1Path, (error as Error).message);
    }
  }

  private async mergeFiles(filePaths: string[], outputPath: string, options: FileOperationOptions): Promise<FileOperationResult> {
    try {
      let mergedContent = '';
      const separator = options.separator || '\n';
      const processedFiles: string[] = [];

      for (const filePath of filePaths) {
        if (await fs.pathExists(filePath)) {
          const content = await fs.readFile(filePath, 'utf8');
          mergedContent += content + separator;
          processedFiles.push(filePath);
        }
      }

      await fs.writeFile(outputPath, mergedContent, 'utf8');
      const stats = await fs.stat(outputPath);

      return {
        success: true,
        path: outputPath,
        operation: 'merge',
        size: stats.size,
        metadata: {
          sourceFiles: processedFiles,
          outputFile: outputPath,
          fileCount: processedFiles.length,
          separator
        }
      };
    } catch (error) {
      throw new FileOperationError('merge', outputPath, (error as Error).message);
    }
  }

  private async splitFile(filePath: string, chunkSize: number, _options?: FileOperationOptions): Promise<FileOperationResult> {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const chunks: string[] = [];
      const outputFiles: string[] = [];

      for (let i = 0; i < content.length; i += chunkSize) {
        chunks.push(content.slice(i, i + chunkSize));
      }

      const baseName = path.basename(filePath, path.extname(filePath));
      const extension = path.extname(filePath);
      const directory = path.dirname(filePath);

      for (let i = 0; i < chunks.length; i++) {
        const chunkFileName = `${baseName}.part${i + 1}${extension}`;
        const chunkFilePath = path.join(directory, chunkFileName);

        await fs.writeFile(chunkFilePath, chunks[i], 'utf8');
        outputFiles.push(chunkFilePath);
      }

      return {
        success: true,
        path: filePath,
        operation: 'split',
        metadata: {
          originalFile: filePath,
          chunkSize,
          chunkCount: chunks.length,
          outputFiles
        }
      };
    } catch (error) {
      throw new FileOperationError('split', filePath, (error as Error).message);
    }
  }

  private async hashFile(filePath: string, algorithm: string = 'sha256', _options?: FileOperationOptions): Promise<FileOperationResult> {
    try {
      const crypto = await import('crypto');
      const content = await fs.readFile(filePath);
      const hash = crypto.createHash(algorithm).update(content).digest('hex');
      const stats = await fs.stat(filePath);

      return {
        success: true,
        path: filePath,
        operation: 'hash',
        size: stats.size,
        metadata: {
          algorithm,
          hash,
          fileSize: stats.size
        }
      };
    } catch (error) {
      throw new FileOperationError('hash', filePath, (error as Error).message);
    }
  }

  private async findFiles(criteria: Record<string, unknown>, searchPath: string, _options?: FileOperationOptions): Promise<FileOperationResult> {
    try {
      const name = criteria.name ? String(criteria.name) : undefined;
      const extension = criteria.extension ? String(criteria.extension) : undefined;
      const minSize = criteria.minSize ? Number(criteria.minSize) : undefined;
      const maxSize = criteria.maxSize ? Number(criteria.maxSize) : undefined;
      const modifiedAfter = criteria.modifiedAfter ? String(criteria.modifiedAfter) : undefined;
      const modifiedBefore = criteria.modifiedBefore ? String(criteria.modifiedBefore) : undefined;
      const content = criteria.content ? String(criteria.content) : undefined;

      let pattern = '**/*';
      if (name) pattern = `**/*${name}*`;
      if (extension) pattern = `**/*.${extension}`;

      const files = await glob(pattern, {
        cwd: searchPath,
        absolute: true,
        onlyFiles: true
      });

      const matchedFiles: Array<{
        path: string;
        size: number;
        modified: Date;
        matches: string[];
      }> = [];

      for (const file of files) {
        const stats = await fs.stat(file);
        let matches = true;
        const matchReasons: string[] = [];

        if (minSize && stats.size < minSize) matches = false;
        if (maxSize && stats.size > maxSize) matches = false;
        if (modifiedAfter && stats.mtime < new Date(modifiedAfter)) matches = false;
        if (modifiedBefore && stats.mtime > new Date(modifiedBefore)) matches = false;

        if (content) {
          try {
            const fileContent = await fs.readFile(file, 'utf8');
            if (fileContent.includes(content)) {
              matchReasons.push('content match');
            } else {
              matches = false;
            }
          } catch {
            matches = false;
          }
        }

        if (matches) {
          matchedFiles.push({
            path: file,
            size: stats.size,
            modified: stats.mtime,
            matches: matchReasons
          });
        }
      }

      return {
        success: true,
        path: searchPath,
        operation: 'find',
        metadata: {
          criteria,
          matchedFiles,
          matchCount: matchedFiles.length,
          totalScanned: files.length
        }
      };
    } catch (error) {
      throw new FileOperationError('find', searchPath, (error as Error).message);
    }
  }

  private async replaceInFiles(pattern: string, replacement: string, searchPath: string, _options?: FileOperationOptions): Promise<FileOperationResult> {
    try {
      const files = await glob('**/*', {
        cwd: searchPath,
        absolute: true,
        onlyFiles: true
      });

      const modifiedFiles: Array<{
        path: string;
        replacements: number;
      }> = [];

      let totalReplacements = 0;

      for (const file of files) {
        try {
          const content = await fs.readFile(file, 'utf8');
          const regex = new RegExp(pattern, 'g');
          const matches = content.match(regex);

          if (matches && matches.length > 0) {
            const newContent = content.replace(regex, replacement);
            await fs.writeFile(file, newContent, 'utf8');

            modifiedFiles.push({
              path: file,
              replacements: matches.length
            });

            totalReplacements += matches.length;
          }
        } catch {
          // Skip files that can't be processed
          continue;
        }
      }

      return {
        success: true,
        path: searchPath,
        operation: 'replace',
        metadata: {
          pattern,
          replacement,
          modifiedFiles,
          totalReplacements,
          filesModified: modifiedFiles.length,
          totalFilesScanned: files.length
        }
      };
    } catch (error) {
      throw new FileOperationError('replace', searchPath, (error as Error).message);
    }
  }

  private async batchOperation(operations: Record<string, unknown>[], context: AgentContext, _options?: FileOperationOptions): Promise<FileOperationResult> {
    try {
      const results: Array<{ operation: unknown; path: unknown; success: boolean; result: unknown }> = [];
      const errors: Array<{ operation: unknown; path: unknown; error: string }> = [];

      for (const operation of operations) {
        try {
          const result = await this.execute(operation, context);
          results.push({
            operation: operation.operation,
            path: operation.path,
            success: result.success,
            result: result.result
          });
        } catch (error) {
          errors.push({
            operation: operation.operation,
            path: operation.path,
            error: (error as Error).message
          });
        }
      }

      const successCount = results.filter(r => r.success).length;
      const failureCount = errors.length + results.filter(r => !r.success).length;

      return {
        success: errors.length === 0,
        path: context.workingDirectory,
        operation: 'batch',
        metadata: {
          totalOperations: operations.length,
          successCount,
          failureCount,
          results,
          errors
        }
      };
    } catch (error) {
      throw new FileOperationError('batch', context.workingDirectory, (error as Error).message);
    }
  }
}
