import { LL<PERSON>rovider, LLMOptions, LLMResponse, <PERSON>l<PERSON>all, <PERSON>lR<PERSON>ult, AgentContext } from '../../types';
export declare class AnthropicProvider implements LLMProvider {
    readonly name = "anthropic";
    private client;
    private logger;
    private toolRegistry;
    constructor(config: Record<string, unknown>);
    generateResponse(prompt: string, options?: LLMOptions): Promise<LLMResponse>;
    generateStreamResponse(prompt: string, options?: LLMOptions): AsyncGenerator<string, void, unknown>;
    supportsToolCalling(): boolean;
    callTool(toolCall: ToolCall, context: AgentContext): Promise<ToolResult>;
    private buildMessages;
    validateApiKey(): Promise<boolean>;
    getAvailableModels(): Promise<string[]>;
    getDefaultModel(): string;
    getMaxTokens(model?: string): number;
    estimateTokens(text: string): number;
    calculateCost(usage: {
        promptTokens: number;
        completionTokens: number;
    }, model?: string): number;
}
//# sourceMappingURL=AnthropicProvider.d.ts.map