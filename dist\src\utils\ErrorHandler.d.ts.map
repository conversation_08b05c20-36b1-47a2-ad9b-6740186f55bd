{"version": 3, "file": "ErrorHandler.d.ts", "sourceRoot": "", "sources": ["../../../src/utils/ErrorHandler.ts"], "names": [], "mappings": "AAEA,qBAAa,UAAW,SAAQ,KAAK;IACnC,SAAgB,IAAI,EAAE,MAAM,CAAC;IAC7B,SAAgB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;gBAElC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;CAMzE;AAED,qBAAa,kBAAmB,SAAQ,UAAU;gBACpC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;CAM7E;AAED,qBAAa,gBAAiB,SAAQ,UAAU;gBAClC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;CAM7E;AAED,qBAAa,YAAa,SAAQ,UAAU;gBAC9B,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;CAG3D;AAED,qBAAa,kBAAmB,SAAQ,UAAU;gBACpC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;CAG3D;AAED,qBAAa,kBAAmB,SAAQ,UAAU;gBACpC,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;CAO5F;AAED,qBAAa,mBAAoB,SAAQ,UAAU;gBACrC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;CAQ7F;AAED,qBAAa,YAAY;IACvB,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAe;IACtC,OAAO,CAAC,MAAM,CAAS;IAEvB,OAAO;WAIO,WAAW,IAAI,YAAY;IAOlC,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI;IAUxD,OAAO,CAAC,gBAAgB;IAkDxB,OAAO,CAAC,kBAAkB;IAUb,gBAAgB,CAAC,CAAC,EAC7B,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAC3B,OAAO,CAAC,EAAE,MAAM,GACf,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;IASb,eAAe,CAAC,CAAC,EACtB,SAAS,EAAE,MAAM,CAAC,EAClB,OAAO,CAAC,EAAE,MAAM,GACf,CAAC,GAAG,IAAI;IASJ,sBAAsB,IAAI,IAAI;CAyBtC"}