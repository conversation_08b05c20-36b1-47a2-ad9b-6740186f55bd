"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigManager = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
const yaml_1 = __importDefault(require("yaml"));
const ErrorHandler_1 = require("../utils/ErrorHandler");
const Logger_1 = require("../utils/Logger");
class ConfigManager {
    static instance;
    config;
    configPath;
    logger;
    constructor() {
        this.logger = Logger_1.Logger.getInstance();
        this.configPath = path_1.default.join(process.cwd(), '.ai-cli', 'config.yaml');
        this.config = this.loadConfig();
    }
    static getInstance() {
        if (!ConfigManager.instance) {
            ConfigManager.instance = new ConfigManager();
        }
        return ConfigManager.instance;
    }
    getDefaultConfig() {
        return {
            agent: {
                provider: 'openai',
                model: 'gpt-4',
                temperature: 0.7,
                maxTokens: 4000,
                enableToolCalling: true,
                enableParallelExecution: true,
                maxParallelTools: 3,
                sessionPersistence: true,
                contextIndexing: true,
                autoDiscovery: true
            },
            providers: {
                openai: {
                    baseURL: 'https://api.openai.com/v1'
                },
                anthropic: {
                    baseURL: 'https://api.anthropic.com'
                },
                deepseek: {
                    baseURL: 'https://api.deepseek.com/v1'
                },
                ollama: {
                    baseURL: 'http://localhost:11434',
                    timeout: 30000
                }
            },
            session: {
                persistenceEnabled: true,
                maxSessions: 50,
                sessionTimeout: 86400000, // 24 hours
                autoCleanup: true
            },
            context: {
                indexingEnabled: true,
                maxFileSize: 1048576, // 1MB
                excludePatterns: [
                    'node_modules/**',
                    '.git/**',
                    'dist/**',
                    'build/**',
                    '*.log',
                    '.env*',
                    '*.tmp',
                    '*.temp'
                ],
                includePatterns: [
                    '**/*.ts',
                    '**/*.js',
                    '**/*.py',
                    '**/*.java',
                    '**/*.cpp',
                    '**/*.c',
                    '**/*.h',
                    '**/*.json',
                    '**/*.yaml',
                    '**/*.yml',
                    '**/*.md',
                    '**/*.txt'
                ],
                watchForChanges: true
            },
            logging: {
                level: 'info',
                enableFileLogging: true,
                maxLogFiles: 10,
                maxLogSize: '5MB'
            }
        };
    }
    loadConfig() {
        try {
            if (fs_extra_1.default.existsSync(this.configPath)) {
                const configContent = fs_extra_1.default.readFileSync(this.configPath, 'utf-8');
                const userConfig = yaml_1.default.parse(configContent);
                return this.mergeConfigs(this.getDefaultConfig(), userConfig);
            }
            else {
                const defaultConfig = this.getDefaultConfig();
                this.saveConfig(defaultConfig);
                return defaultConfig;
            }
        }
        catch (error) {
            this.logger.error('Failed to load configuration', { error: error.message });
            throw new ErrorHandler_1.ConfigurationError(`Failed to load configuration: ${error.message}`);
        }
    }
    mergeConfigs(defaultConfig, userConfig) {
        return {
            agent: { ...defaultConfig.agent, ...userConfig.agent },
            providers: {
                openai: { ...defaultConfig.providers.openai, ...userConfig.providers?.openai },
                anthropic: { ...defaultConfig.providers.anthropic, ...userConfig.providers?.anthropic },
                deepseek: { ...defaultConfig.providers.deepseek, ...userConfig.providers?.deepseek },
                gemini: { ...defaultConfig.providers.gemini, ...userConfig.providers?.gemini },
                mistral: { ...defaultConfig.providers.mistral, ...userConfig.providers?.mistral },
                ollama: { ...defaultConfig.providers.ollama, ...userConfig.providers?.ollama }
            },
            session: { ...defaultConfig.session, ...userConfig.session },
            context: { ...defaultConfig.context, ...userConfig.context },
            logging: { ...defaultConfig.logging, ...userConfig.logging }
        };
    }
    saveConfig(config) {
        try {
            fs_extra_1.default.ensureDirSync(path_1.default.dirname(this.configPath));
            const configContent = yaml_1.default.stringify(config, { indent: 2 });
            fs_extra_1.default.writeFileSync(this.configPath, configContent, 'utf-8');
            this.logger.info('Configuration saved', { path: this.configPath });
        }
        catch (error) {
            this.logger.error('Failed to save configuration', { error: error.message });
            throw new ErrorHandler_1.ConfigurationError(`Failed to save configuration: ${error.message}`);
        }
    }
    getConfig() {
        return this.config;
    }
    getAgentConfig() {
        return this.config.agent;
    }
    getProviderConfig(provider) {
        return this.config.providers[provider];
    }
    updateConfig(updates) {
        this.config = this.mergeConfigs(this.config, updates);
        this.saveConfig(this.config);
        this.logger.info('Configuration updated');
    }
    updateAgentConfig(updates) {
        this.config.agent = { ...this.config.agent, ...updates };
        this.saveConfig(this.config);
        this.logger.info('Agent configuration updated');
    }
    setProviderApiKey(provider, apiKey) {
        if (!this.config.providers[provider]) {
            this.config.providers[provider] = {};
        }
        this.config.providers[provider].apiKey = apiKey;
        this.saveConfig(this.config);
        this.logger.info(`API key set for provider: ${provider}`);
    }
    validateConfig() {
        try {
            const requiredProviderConfig = this.getProviderConfig(this.config.agent.provider);
            if (!requiredProviderConfig) {
                throw new ErrorHandler_1.ConfigurationError(`Provider configuration missing: ${this.config.agent.provider}`);
            }
            if (this.config.agent.provider !== 'ollama' && !requiredProviderConfig.apiKey) {
                throw new ErrorHandler_1.ConfigurationError(`API key missing for provider: ${this.config.agent.provider}`);
            }
            return true;
        }
        catch (error) {
            this.logger.error('Configuration validation failed', { error: error.message });
            return false;
        }
    }
    getConfigPath() {
        return this.configPath;
    }
}
exports.ConfigManager = ConfigManager;
//# sourceMappingURL=ConfigManager.js.map