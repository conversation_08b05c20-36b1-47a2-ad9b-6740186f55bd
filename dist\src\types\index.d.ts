export interface LLMProvider {
    name: string;
    generateResponse(_prompt: string, _options?: LLMOptions): Promise<LLMResponse>;
    generateStreamResponse(_prompt: string, _options?: LLMOptions): AsyncGenerator<string, void, unknown>;
    supportsToolCalling(): boolean;
    callTool(_toolCall: ToolCall, _context: AgentContext): Promise<ToolResult>;
}
export interface LLMOptions {
    temperature?: number;
    maxTokens?: number;
    model?: string;
    tools?: Tool[];
    systemPrompt?: string;
    conversationHistory?: Message[];
}
export interface LLMResponse {
    content: string;
    toolCalls?: ToolCall[];
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    } | undefined;
}
export interface Message {
    role: 'system' | 'user' | 'assistant' | 'tool';
    content: string;
    toolCallId?: string;
    name?: string;
}
export interface Tool {
    name: string;
    description: string;
    parameters: ToolParameters;
    execute: (_args: Record<string, unknown>, _context: AgentContext) => Promise<ToolResult>;
}
export interface ToolParameters {
    type: 'object';
    properties: Record<string, ToolProperty>;
    required?: string[];
    [key: string]: unknown;
}
export interface ToolProperty {
    type: string;
    description: string;
    enum?: string[];
    items?: ToolProperty;
    properties?: Record<string, ToolProperty>;
}
export interface ToolCall {
    id: string;
    name: string;
    arguments: Record<string, unknown>;
}
export interface ToolResult {
    success: boolean;
    result: unknown;
    error?: string;
    metadata?: Record<string, unknown>;
}
export interface AgentContext {
    sessionId: string;
    workingDirectory: string;
    projectContext: ProjectContext;
    conversationHistory: Message[];
    availableTools: Tool[];
    config: AgentConfig;
}
export interface ProjectContext {
    rootPath: string;
    projectType: string;
    dependencies: Record<string, string>;
    files: FileInfo[];
    gitInfo?: GitInfo | undefined;
    packageInfo?: PackageInfo | undefined;
}
export interface FileInfo {
    path: string;
    type: 'file' | 'directory';
    size: number;
    lastModified: Date;
    permissions: string;
    content?: string;
}
export interface GitInfo {
    branch: string;
    remotes: string[];
    status: string;
    lastCommit: string;
}
export interface PackageInfo {
    name: string;
    version: string;
    dependencies: Record<string, string>;
    devDependencies: Record<string, string>;
    scripts: Record<string, string>;
}
export interface AgentConfig {
    provider: string;
    model: string;
    temperature: number;
    maxTokens: number;
    enableToolCalling: boolean;
    enableParallelExecution: boolean;
    maxParallelTools: number;
    sessionPersistence: boolean;
    contextIndexing: boolean;
    autoDiscovery: boolean;
}
export interface Session {
    id: string;
    createdAt: Date;
    lastAccessedAt: Date;
    workingDirectory: string;
    context: AgentContext;
    conversationHistory: Message[];
    metadata: Record<string, unknown>;
}
export interface CLICommand {
    name: string;
    description: string;
    options?: CLIOption[];
    action: (_args: Record<string, unknown>, _context: AgentContext) => Promise<void>;
}
export interface CLIOption {
    name: string;
    description: string;
    type: 'string' | 'number' | 'boolean';
    required?: boolean;
    default?: unknown;
}
export interface ShellExecutionOptions {
    cwd?: string;
    env?: Record<string, string>;
    timeout?: number;
    shell?: string;
    encoding?: string;
    maxBuffer?: number;
    killSignal?: string;
}
export interface ShellExecutionResult {
    stdout: string;
    stderr: string;
    exitCode: number;
    command: string;
    duration: number;
    success: boolean;
}
export interface FileOperationOptions {
    encoding?: BufferEncoding;
    recursive?: boolean;
    force?: boolean;
    preserveTimestamps?: boolean;
    overwrite?: boolean;
    createDirectories?: boolean;
    format?: string;
    compressionLevel?: number;
    separator?: string;
    [key: string]: unknown;
}
export interface FileOperationResult {
    success: boolean;
    path: string;
    operation: string;
    size?: number;
    error?: string;
    metadata?: Record<string, unknown>;
}
export interface SearchOptions {
    pattern: string;
    caseSensitive?: boolean;
    wholeWord?: boolean;
    regex?: boolean;
    includeFiles?: string[];
    excludeFiles?: string[];
    maxResults?: number;
    context?: number;
}
export interface SearchResult {
    file: string;
    line: number;
    column: number;
    match: string;
    context: string[];
}
export interface LogLevel {
    ERROR: 'error';
    WARN: 'warn';
    INFO: 'info';
    DEBUG: 'debug';
    VERBOSE: 'verbose';
}
export interface LogEntry {
    level: keyof LogLevel;
    message: string;
    timestamp: Date;
    metadata?: Record<string, unknown>;
    sessionId?: string;
}
export type BufferEncoding = 'ascii' | 'utf8' | 'utf-8' | 'utf16le' | 'ucs2' | 'ucs-2' | 'base64' | 'base64url' | 'latin1' | 'binary' | 'hex';
export type ErrorWithMessage = {
    message: string;
    stack?: string;
    name?: string;
    code?: string | number;
};
export type UnknownRecord = Record<string, unknown>;
export type StringRecord = Record<string, string>;
export type AnyFunction = (..._args: unknown[]) => unknown;
export type AsyncFunction<T = unknown> = (..._args: unknown[]) => Promise<T>;
//# sourceMappingURL=index.d.ts.map