"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const AgentOrchestrator_1 = require("../../src/agents/AgentOrchestrator");
const ConfigManager_1 = require("../../src/config/ConfigManager");
const SessionManager_1 = require("../../src/session/SessionManager");
const ToolRegistry_1 = require("../../src/tools/ToolRegistry");
const ContextEngine_1 = require("../../src/context/ContextEngine");
const path_1 = __importDefault(require("path"));
const fs_extra_1 = __importDefault(require("fs-extra"));
describe('Basic Functionality Integration Tests', () => {
    let agentOrchestrator;
    let configManager;
    let sessionManager;
    let toolRegistry;
    let contextEngine;
    let testDir;
    beforeAll(async () => {
        // Create test directory
        testDir = path_1.default.join(__dirname, 'test-workspace');
        await fs_extra_1.default.ensureDir(testDir);
        // Initialize components
        configManager = ConfigManager_1.ConfigManager.getInstance();
        sessionManager = SessionManager_1.SessionManager.getInstance();
        toolRegistry = ToolRegistry_1.ToolRegistry.getInstance();
        contextEngine = ContextEngine_1.ContextEngine.getInstance();
        agentOrchestrator = AgentOrchestrator_1.AgentOrchestrator.getInstance();
        // Set up basic configuration for testing
        configManager.updateAgentConfig({
            provider: 'openai',
            model: 'gpt-3.5-turbo',
            maxTokens: 1000,
            temperature: 0.1
        });
    });
    afterAll(async () => {
        // Cleanup
        await fs_extra_1.default.remove(testDir);
        agentOrchestrator.cleanup();
    });
    describe('Component Initialization', () => {
        test('should initialize all core components', () => {
            expect(configManager).toBeDefined();
            expect(sessionManager).toBeDefined();
            expect(toolRegistry).toBeDefined();
            expect(contextEngine).toBeDefined();
            expect(agentOrchestrator).toBeDefined();
        });
        test('should have singleton instances', () => {
            expect(ConfigManager_1.ConfigManager.getInstance()).toBe(configManager);
            expect(SessionManager_1.SessionManager.getInstance()).toBe(sessionManager);
            expect(ToolRegistry_1.ToolRegistry.getInstance()).toBe(toolRegistry);
            expect(ContextEngine_1.ContextEngine.getInstance()).toBe(contextEngine);
            expect(AgentOrchestrator_1.AgentOrchestrator.getInstance()).toBe(agentOrchestrator);
        });
    });
    describe('Tool Registry', () => {
        test('should have registered tools', () => {
            const tools = toolRegistry.getAllTools();
            expect(tools.length).toBeGreaterThan(0);
            const toolNames = tools.map(tool => tool.name);
            expect(toolNames).toContain('file');
            expect(toolNames).toContain('shell');
        });
        test('should get tool by name', () => {
            const fileTool = toolRegistry.getTool('file');
            expect(fileTool).toBeDefined();
            expect(fileTool?.name).toBe('file');
        });
    });
    describe('File Tool Operations', () => {
        test('should create and read files', async () => {
            const testFile = path_1.default.join(testDir, 'test.txt');
            const testContent = 'Hello, World!';
            // Create context for tool execution
            const context = await contextEngine.createAgentContext('test-session', testDir);
            // Write file
            const writeResult = await toolRegistry.executeTool({
                name: 'file',
                id: 'test-write',
                arguments: {
                    operation: 'write',
                    path: 'test.txt',
                    content: testContent
                }
            }, context);
            expect(writeResult.success).toBe(true);
            expect(await fs_extra_1.default.pathExists(testFile)).toBe(true);
            // Read file
            const readResult = await toolRegistry.executeTool({
                name: 'file',
                id: 'test-read',
                arguments: {
                    operation: 'read',
                    path: 'test.txt'
                }
            }, context);
            expect(readResult.success).toBe(true);
            expect(readResult.result.metadata.content).toBe(testContent);
        });
        test('should handle file operations with options', async () => {
            const context = await contextEngine.createAgentContext('test-session-2', testDir);
            // Test file exists check
            const existsResult = await toolRegistry.executeTool({
                name: 'file',
                id: 'test-exists',
                arguments: {
                    operation: 'exists',
                    path: 'test.txt'
                }
            }, context);
            expect(existsResult.success).toBe(true);
            expect(existsResult.result.metadata.exists).toBe(true);
        });
    });
    describe('Context Engine', () => {
        test('should create agent context', async () => {
            const context = await contextEngine.createAgentContext('test-context', testDir);
            expect(context).toBeDefined();
            expect(context.sessionId).toBe('test-context');
            expect(context.workingDirectory).toBe(testDir);
            expect(context.projectContext).toBeDefined();
            expect(context.availableTools.length).toBeGreaterThan(0);
        });
        test('should get project context', () => {
            const projectContext = contextEngine.getProjectContext(testDir);
            expect(projectContext).toBeDefined();
            expect(projectContext?.rootPath).toBe(testDir);
        });
    });
    describe('Session Management', () => {
        test('should create and manage sessions', async () => {
            const context = await contextEngine.createAgentContext('session-test', testDir);
            const session = await sessionManager.createSession(testDir, context);
            expect(session).toBeDefined();
            expect(session.id).toBeDefined();
            expect(session.workingDirectory).toBe(testDir);
            expect(session.conversationHistory).toEqual([]);
        });
        test('should list sessions', () => {
            const sessions = sessionManager.listSessions();
            expect(Array.isArray(sessions)).toBe(true);
        });
    });
    describe('Configuration Management', () => {
        test('should manage configuration', () => {
            const config = configManager.getAgentConfig();
            expect(config).toBeDefined();
            expect(config.provider).toBe('openai');
            expect(config.model).toBe('gpt-3.5-turbo');
        });
        test('should update configuration', () => {
            configManager.updateAgentConfig({ temperature: 0.5 });
            const config = configManager.getAgentConfig();
            expect(config.temperature).toBe(0.5);
        });
    });
    describe('Enhanced Tool Chain Execution', () => {
        test('should execute tool chain with enhanced features', async () => {
            const context = await contextEngine.createAgentContext('chain-test', testDir);
            const toolCalls = [
                {
                    name: 'file',
                    id: 'chain-1',
                    arguments: {
                        operation: 'write',
                        path: 'chain-test.txt',
                        content: 'Chain test content'
                    }
                },
                {
                    name: 'file',
                    id: 'chain-2',
                    arguments: {
                        operation: 'read',
                        path: 'chain-test.txt'
                    }
                }
            ];
            const results = await toolRegistry.executeToolChain(toolCalls, context, {
                enableLearning: true,
                adaptiveExecution: true,
                maxParallel: 2
            });
            expect(results).toHaveLength(2);
            expect(results.every(r => r.success)).toBe(true);
        });
    });
});
//# sourceMappingURL=basic-functionality.test.js.map