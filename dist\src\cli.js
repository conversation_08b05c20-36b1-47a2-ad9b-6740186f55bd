#!/usr/bin/env node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const commander_1 = require("commander");
const inquirer_1 = __importDefault(require("inquirer"));
const chalk_1 = __importDefault(require("chalk"));
const ora_1 = __importDefault(require("ora"));
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
const AgentOrchestrator_1 = require("./agents/AgentOrchestrator");
const ConfigManager_1 = require("./config/ConfigManager");
const SessionManager_1 = require("./session/SessionManager");
const ErrorHandler_1 = require("./utils/ErrorHandler");
// import { Logger } from './utils/Logger';
// Load environment variables
dotenv_1.default.config();
const program = new commander_1.Command();
// const _logger = Logger.getInstance();
const errorHandler = ErrorHandler_1.ErrorHandler.getInstance();
const configManager = ConfigManager_1.ConfigManager.getInstance();
const sessionManager = SessionManager_1.SessionManager.getInstance();
const agentOrchestrator = AgentOrchestrator_1.AgentOrchestrator.getInstance();
// Setup graceful shutdown
errorHandler.createGracefulShutdown();
program
    .name('ai-cli')
    .description('Autonomous AI-Powered CLI Tool System')
    .version('1.0.0');
// Main chat command
program
    .command('chat')
    .description('Start an interactive AI chat session')
    .option('-d, --directory <path>', 'Working directory', process.cwd())
    .option('-p, --provider <provider>', 'LLM provider to use')
    .option('-m, --model <model>', 'Model to use')
    .option('-s, --session <sessionId>', 'Resume existing session')
    .option('--autonomous', 'Enable autonomous mode for complex tasks')
    .option('--max-iterations <iterations>', 'Maximum iterations for autonomous mode', parseInt)
    .action(async (options) => {
    const spinner = (0, ora_1.default)('Initializing AI CLI...').start();
    try {
        // Set working directory
        const workingDirectory = path_1.default.resolve(options.directory);
        process.chdir(workingDirectory);
        // Update config if provider/model specified
        if (options.provider) {
            configManager.updateAgentConfig({ provider: options.provider });
        }
        if (options.model) {
            configManager.updateAgentConfig({ model: options.model });
        }
        // Validate configuration
        if (!configManager.validateConfig()) {
            spinner.fail('Configuration validation failed');
            console.log(chalk_1.default.yellow('\nPlease run "ai-cli config" to set up your API keys.'));
            return;
        }
        // Initialize or resume session
        let context;
        if (options.session) {
            spinner.text = 'Resuming session...';
            const session = await sessionManager.loadSession(options.session);
            context = session.context;
        }
        else {
            spinner.text = 'Creating new session...';
            context = await agentOrchestrator.initialize(workingDirectory);
        }
        spinner.succeed('AI CLI initialized successfully!');
        // Display welcome message
        console.log(chalk_1.default.blue.bold('\n🤖 AI CLI Agent - Autonomous Assistant'));
        console.log(chalk_1.default.gray('Type "help" for commands, "exit" to quit\n'));
        const sessionInfo = agentOrchestrator.getSessionInfo();
        if (sessionInfo) {
            console.log(chalk_1.default.cyan(`📁 Working Directory: ${sessionInfo.workingDirectory}`));
            console.log(chalk_1.default.cyan(`🏗️  Project Type: ${sessionInfo.projectType}`));
            console.log(chalk_1.default.cyan(`💬 Session: ${sessionInfo.sessionId.substring(0, 8)}...`));
            console.log('');
        }
        // Start interactive chat loop
        await startChatLoop(context);
    }
    catch (error) {
        spinner.fail('Failed to initialize AI CLI');
        errorHandler.handleError(error, 'CLI initialization');
    }
});
// Configuration command
program
    .command('config')
    .description('Configure AI CLI settings')
    .option('--provider <provider>', 'Set default LLM provider')
    .option('--model <model>', 'Set default model')
    .option('--api-key <key>', 'Set API key for current provider')
    .option('--list', 'List current configuration')
    .action(async (options) => {
    try {
        if (options.list) {
            const config = configManager.getConfig();
            console.log(chalk_1.default.blue.bold('\n🔧 Current Configuration:'));
            console.log(chalk_1.default.cyan(`Provider: ${config.agent.provider}`));
            console.log(chalk_1.default.cyan(`Model: ${config.agent.model}`));
            console.log(chalk_1.default.cyan(`Temperature: ${config.agent.temperature}`));
            console.log(chalk_1.default.cyan(`Max Tokens: ${config.agent.maxTokens}`));
            console.log(chalk_1.default.cyan(`Tool Calling: ${config.agent.enableToolCalling ? 'Enabled' : 'Disabled'}`));
            console.log(chalk_1.default.cyan(`Parallel Execution: ${config.agent.enableParallelExecution ? 'Enabled' : 'Disabled'}`));
            return;
        }
        if (options.provider) {
            configManager.updateAgentConfig({ provider: options.provider });
            console.log(chalk_1.default.green(`✅ Provider set to: ${options.provider}`));
        }
        if (options.model) {
            configManager.updateAgentConfig({ model: options.model });
            console.log(chalk_1.default.green(`✅ Model set to: ${options.model}`));
        }
        if (options.apiKey) {
            const provider = options.provider || configManager.getAgentConfig().provider;
            configManager.setProviderApiKey(provider, options.apiKey);
            console.log(chalk_1.default.green(`✅ API key set for: ${provider}`));
        }
        if (!options.provider && !options.model && !options.apiKey) {
            await interactiveConfig();
        }
    }
    catch (error) {
        errorHandler.handleError(error, 'Configuration');
    }
});
// Session management commands
program
    .command('sessions')
    .description('Manage chat sessions')
    .option('--list', 'List all sessions')
    .option('--delete <sessionId>', 'Delete a session')
    .option('--export <sessionId>', 'Export a session')
    .option('--import <filePath>', 'Import a session')
    .action(async (options) => {
    try {
        if (options.list) {
            const sessions = sessionManager.listSessions();
            console.log(chalk_1.default.blue.bold('\n📝 Chat Sessions:'));
            if (sessions.length === 0) {
                console.log(chalk_1.default.gray('No sessions found.'));
                return;
            }
            for (const session of sessions.slice(0, 10)) {
                const age = Math.floor((Date.now() - session.lastAccessedAt.getTime()) / (1000 * 60 * 60));
                console.log(chalk_1.default.cyan(`${session.id.substring(0, 8)}... - ${session.workingDirectory} (${age}h ago)`));
            }
            return;
        }
        if (options.delete) {
            const deleted = await sessionManager.deleteSession(options.delete);
            if (deleted) {
                console.log(chalk_1.default.green(`✅ Session deleted: ${options.delete}`));
            }
            else {
                console.log(chalk_1.default.red(`❌ Session not found: ${options.delete}`));
            }
            return;
        }
        if (options.export) {
            const filePath = `session-${options.export.substring(0, 8)}.json`;
            await sessionManager.exportSession(options.export, filePath);
            console.log(chalk_1.default.green(`✅ Session exported to: ${filePath}`));
            return;
        }
        if (options.import) {
            const session = await sessionManager.importSession(options.import);
            console.log(chalk_1.default.green(`✅ Session imported: ${session.id}`));
            return;
        }
    }
    catch (error) {
        errorHandler.handleError(error, 'Session management');
    }
});
// Status command
program
    .command('status')
    .description('Show AI CLI status and diagnostics')
    .action(async () => {
    try {
        const spinner = (0, ora_1.default)('Checking status...').start();
        const providerStatus = await agentOrchestrator.getProviderStatus();
        const sessionStats = sessionManager.getSessionStats();
        spinner.succeed('Status check completed');
        console.log(chalk_1.default.blue.bold('\n🔍 AI CLI Status:'));
        console.log(chalk_1.default.cyan(`Current Provider: ${providerStatus.current}`));
        console.log(chalk_1.default.cyan(`Available Providers: ${providerStatus.available.join(', ')}`));
        console.log(chalk_1.default.blue.bold('\n🔌 Provider Status:'));
        for (const [provider, working] of Object.entries(providerStatus.working)) {
            const status = working ? chalk_1.default.green('✅ Working') : chalk_1.default.red('❌ Not working');
            console.log(chalk_1.default.cyan(`${provider}: ${status}`));
        }
        console.log(chalk_1.default.blue.bold('\n📊 Session Statistics:'));
        console.log(chalk_1.default.cyan(`Total Sessions: ${sessionStats.totalSessions}`));
        console.log(chalk_1.default.cyan(`Active Sessions: ${sessionStats.activeSessions}`));
        console.log(chalk_1.default.cyan(`Total Messages: ${sessionStats.totalMessages}`));
    }
    catch (error) {
        errorHandler.handleError(error, 'Status check');
    }
});
// Autonomous task execution command
program
    .command('task <description>')
    .description('Execute an autonomous task')
    .option('-d, --directory <path>', 'Working directory', process.cwd())
    .option('-p, --provider <provider>', 'LLM provider to use')
    .option('--max-iterations <iterations>', 'Maximum iterations', parseInt, 10)
    .option('--save-progress', 'Save task progress')
    .option('--learning', 'Enable learning mode')
    .action(async (description, options) => {
    const spinner = (0, ora_1.default)('Initializing autonomous task...').start();
    try {
        const workingDirectory = path_1.default.resolve(options.directory);
        if (options.provider) {
            configManager.updateAgentConfig({ provider: options.provider });
        }
        if (!configManager.validateConfig()) {
            spinner.fail('Configuration validation failed');
            console.log(chalk_1.default.yellow('\nPlease run "ai-cli config" to set up your API keys.'));
            return;
        }
        await agentOrchestrator.initialize(workingDirectory);
        spinner.text = 'Executing autonomous task...';
        const result = await agentOrchestrator.executeAutonomousTask(description, workingDirectory, {
            maxIterations: options.maxIterations,
            enableLearning: options.learning,
            saveProgress: options.saveProgress
        });
        spinner.succeed(`Task completed in ${result.iterations} iterations`);
        console.log(chalk_1.default.blue.bold('\n🤖 Autonomous Task Results:'));
        console.log(chalk_1.default.cyan(`Success: ${result.success ? '✅' : '❌'}`));
        console.log(chalk_1.default.cyan(`Iterations: ${result.iterations}`));
        console.log(chalk_1.default.cyan(`Final Response:`));
        console.log(chalk_1.default.white(result.finalResponse));
        if (result.results.length > 0) {
            console.log(chalk_1.default.blue.bold('\n📊 Execution Summary:'));
            result.results.forEach((res, i) => {
                console.log(chalk_1.default.gray(`${i + 1}. ${res.task.substring(0, 80)}...`));
            });
        }
    }
    catch (error) {
        spinner.fail('Autonomous task failed');
        errorHandler.handleError(error, 'Autonomous task execution');
    }
});
// Advanced autonomous task command with enhanced features
program
    .command('advanced-task <description>')
    .alias('adv-task')
    .description('Execute advanced autonomous task with enhanced AI capabilities')
    .option('-d, --directory <path>', 'Working directory', process.cwd())
    .option('-p, --provider <provider>', 'LLM provider to use')
    .option('--max-iterations <iterations>', 'Maximum iterations', parseInt, 15)
    .option('--save-progress', 'Save task progress')
    .option('--learning', 'Enable learning mode')
    .option('--adaptive', 'Enable adaptive strategy')
    .option('--context-awareness', 'Enable context awareness')
    .option('--self-correction', 'Enable self-correction')
    .action(async (description, options) => {
    const spinner = (0, ora_1.default)('Initializing advanced autonomous task...').start();
    try {
        const workingDirectory = path_1.default.resolve(options.directory);
        if (options.provider) {
            configManager.updateAgentConfig({ provider: options.provider });
        }
        if (!configManager.validateConfig()) {
            spinner.fail('Configuration validation failed');
            console.log(chalk_1.default.yellow('\nPlease run "ai-cli config" to set up your API keys.'));
            return;
        }
        await agentOrchestrator.initialize(workingDirectory);
        spinner.text = 'Executing advanced autonomous task...';
        const result = await agentOrchestrator.executeAdvancedAutonomousTask(description, workingDirectory, {
            maxIterations: options.maxIterations,
            enableLearning: options.learning,
            saveProgress: options.saveProgress,
            adaptiveStrategy: options.adaptive,
            contextAwareness: options.contextAwareness,
            selfCorrection: options.selfCorrection
        });
        spinner.succeed(`Advanced task completed in ${result.iterations} iterations`);
        console.log(chalk_1.default.blue.bold('\n🚀 Advanced Autonomous Task Results:'));
        console.log(chalk_1.default.cyan(`Success: ${result.success ? '✅' : '❌'}`));
        console.log(chalk_1.default.cyan(`Iterations: ${result.iterations}`));
        console.log(chalk_1.default.cyan(`Final Response:`));
        console.log(chalk_1.default.white(result.finalResponse));
        if (result.insights.length > 0) {
            console.log(chalk_1.default.blue.bold('\n💡 Insights:'));
            result.insights.forEach(insight => console.log(chalk_1.default.cyan(`  • ${insight}`)));
        }
        if (result.recommendations.length > 0) {
            console.log(chalk_1.default.blue.bold('\n📋 Recommendations:'));
            result.recommendations.forEach(rec => console.log(chalk_1.default.yellow(`  • ${rec}`)));
        }
        if (result.results.length > 0) {
            console.log(chalk_1.default.blue.bold('\n📊 Execution Summary:'));
            result.results.forEach((res, i) => {
                console.log(chalk_1.default.gray(`${i + 1}. ${res.task.substring(0, 80)}...`));
            });
        }
    }
    catch (error) {
        spinner.fail('Advanced autonomous task failed');
        errorHandler.handleError(error, 'Advanced autonomous task execution');
    }
});
// Workflow execution command
program
    .command('workflow <name>')
    .description('Execute predefined autonomous workflow')
    .option('-d, --directory <path>', 'Working directory', process.cwd())
    .option('-p, --provider <provider>', 'LLM provider to use')
    .option('--continue-on-failure', 'Continue workflow even if steps fail')
    .option('--max-retries <retries>', 'Maximum retries per step', parseInt, 2)
    .option('--timeout <seconds>', 'Workflow timeout in seconds', parseInt, 300)
    .action(async (name, options) => {
    const spinner = (0, ora_1.default)('Loading workflow...').start();
    try {
        const workingDirectory = path_1.default.resolve(options.directory);
        if (options.provider) {
            configManager.updateAgentConfig({ provider: options.provider });
        }
        if (!configManager.validateConfig()) {
            spinner.fail('Configuration validation failed');
            console.log(chalk_1.default.yellow('\nPlease run "ai-cli config" to set up your API keys.'));
            return;
        }
        await agentOrchestrator.initialize(workingDirectory);
        // Load predefined workflows (this would be from a config file or database)
        const workflows = {
            'code-review': {
                name: 'Code Review',
                description: 'Comprehensive code review and analysis',
                steps: [
                    { name: 'file_read', id: '1', arguments: { pattern: '**/*.{js,ts,py,java,cpp}' } },
                    { name: 'shell_execute', id: '2', arguments: { command: 'git log --oneline -10' } },
                    { name: 'file_search', id: '3', arguments: { pattern: 'TODO|FIXME|BUG', directory: '.' } }
                ]
            },
            'project-setup': {
                name: 'Project Setup',
                description: 'Initialize new project with best practices',
                steps: [
                    { name: 'file_create', id: '1', arguments: { path: 'README.md', content: '# New Project\n\nProject description here.' } },
                    { name: 'file_create', id: '2', arguments: { path: '.gitignore', content: 'node_modules/\n.env\n*.log' } },
                    { name: 'shell_execute', id: '3', arguments: { command: 'git init' } }
                ]
            }
        };
        const workflow = workflows[name];
        if (!workflow) {
            spinner.fail(`Workflow '${name}' not found`);
            console.log(chalk_1.default.yellow(`Available workflows: ${Object.keys(workflows).join(', ')}`));
            return;
        }
        spinner.text = `Executing workflow: ${workflow.name}...`;
        const toolRegistry = agentOrchestrator.toolRegistry;
        const context = {
            sessionId: `workflow-${Date.now()}`,
            workingDirectory,
            projectContext: null,
            conversationHistory: [],
            availableTools: [],
            config: configManager.getAgentConfig()
        };
        const result = await toolRegistry.executeAutonomousWorkflow({
            ...workflow,
            conditions: {
                continueOnFailure: options.continueOnFailure,
                maxRetries: options.maxRetries,
                timeout: options.timeout * 1000
            }
        }, context);
        spinner.succeed(`Workflow completed: ${result.stepsCompleted}/${workflow.steps.length} steps`);
        console.log(chalk_1.default.blue.bold('\n⚙️ Workflow Results:'));
        console.log(chalk_1.default.cyan(`Success: ${result.success ? '✅' : '❌'}`));
        console.log(chalk_1.default.cyan(`Steps Completed: ${result.stepsCompleted}/${workflow.steps.length}`));
        console.log(chalk_1.default.cyan(`Execution Time: ${result.executionTime}ms`));
        if (result.results.length > 0) {
            console.log(chalk_1.default.blue.bold('\n📊 Step Results:'));
            result.results.forEach((res, i) => {
                const status = res.success ? '✅' : '❌';
                console.log(chalk_1.default.gray(`${i + 1}. ${status} ${workflow.steps[i]?.name || 'Unknown'}`));
            });
        }
    }
    catch (error) {
        spinner.fail('Workflow execution failed');
        errorHandler.handleError(error, 'Workflow execution');
    }
});
// Project analysis command
program
    .command('analyze [directory]')
    .description('Analyze project health and structure')
    .option('--detailed', 'Show detailed analysis')
    .action(async (directory, options) => {
    const spinner = (0, ora_1.default)('Analyzing project...').start();
    try {
        const workingDirectory = path_1.default.resolve(directory || process.cwd());
        await agentOrchestrator.initialize(workingDirectory);
        const health = await agentOrchestrator.analyzeProjectHealth(workingDirectory);
        spinner.succeed('Project analysis completed');
        console.log(chalk_1.default.blue.bold('\n🔍 Project Health Analysis:'));
        console.log(chalk_1.default.cyan(`Health Score: ${health.score}/100`));
        if (health.issues.length > 0) {
            console.log(chalk_1.default.red.bold('\n⚠️  Issues Found:'));
            health.issues.forEach(issue => {
                console.log(chalk_1.default.red(`• ${issue}`));
            });
        }
        if (health.recommendations.length > 0) {
            console.log(chalk_1.default.yellow.bold('\n💡 Recommendations:'));
            health.recommendations.forEach(rec => {
                console.log(chalk_1.default.yellow(`• ${rec}`));
            });
        }
        if (options.detailed) {
            console.log(chalk_1.default.blue.bold('\n📈 Metrics:'));
            Object.entries(health.metrics).forEach(([key, value]) => {
                console.log(chalk_1.default.cyan(`${key}: ${value}`));
            });
        }
    }
    catch (error) {
        spinner.fail('Project analysis failed');
        errorHandler.handleError(error, 'Project analysis');
    }
});
// Context management command
program
    .command('context')
    .description('Manage project context')
    .option('--refresh', 'Refresh project context')
    .option('--snapshot', 'Save context snapshot')
    .option('--history', 'Show context history')
    .option('--restore <index>', 'Restore from snapshot', parseInt)
    .option('-d, --directory <path>', 'Working directory', process.cwd())
    .action(async (options) => {
    try {
        const workingDirectory = path_1.default.resolve(options.directory);
        await agentOrchestrator.initialize(workingDirectory);
        if (options.refresh) {
            const spinner = (0, ora_1.default)('Refreshing context...').start();
            await agentOrchestrator.refreshProjectContext();
            spinner.succeed('Context refreshed');
            return;
        }
        if (options.snapshot) {
            const spinner = (0, ora_1.default)('Saving context snapshot...').start();
            const contextEngine = agentOrchestrator.contextEngine;
            await contextEngine.saveContextSnapshot(workingDirectory);
            spinner.succeed('Context snapshot saved');
            return;
        }
        if (options.history) {
            const contextEngine = agentOrchestrator.contextEngine;
            const history = contextEngine.getContextHistory(workingDirectory);
            console.log(chalk_1.default.blue.bold('\n📚 Context History:'));
            if (history.length === 0) {
                console.log(chalk_1.default.gray('No context snapshots found.'));
            }
            else {
                history.forEach((snapshot, index) => {
                    const date = new Date(snapshot.lastUpdated).toLocaleString();
                    console.log(chalk_1.default.cyan(`${index}: ${date} - ${snapshot.files?.length || 0} files`));
                });
            }
            return;
        }
        if (typeof options.restore === 'number') {
            const spinner = (0, ora_1.default)('Restoring context snapshot...').start();
            const contextEngine = agentOrchestrator.contextEngine;
            const restored = contextEngine.restoreContextSnapshot(workingDirectory, options.restore);
            if (restored) {
                spinner.succeed(`Context restored from snapshot ${options.restore}`);
            }
            else {
                spinner.fail(`Failed to restore snapshot ${options.restore}`);
            }
            return;
        }
        // Show context summary
        const contextEngine = agentOrchestrator.contextEngine;
        const context = contextEngine.getProjectContext(workingDirectory);
        const metrics = contextEngine.getContextMetrics(workingDirectory);
        console.log(chalk_1.default.blue.bold('\n📁 Context Summary:'));
        if (context) {
            console.log(chalk_1.default.cyan(`Project Type: ${context.projectType}`));
            console.log(chalk_1.default.cyan(`Files: ${context.files.length}`));
            console.log(chalk_1.default.cyan(`Dependencies: ${Object.keys(context.dependencies).length}`));
            console.log(chalk_1.default.cyan(`Git: ${context.gitInfo ? 'Yes' : 'No'}`));
        }
        if (metrics) {
            console.log(chalk_1.default.blue.bold('\n📊 Context Metrics:'));
            console.log(chalk_1.default.cyan(`File Count: ${metrics.fileCount}`));
            console.log(chalk_1.default.cyan(`Total Size: ${(metrics.totalSize / 1024 / 1024).toFixed(2)} MB`));
            console.log(chalk_1.default.cyan(`Cache Hit Rate: ${(metrics.cacheHitRate * 100).toFixed(1)}%`));
            console.log(chalk_1.default.cyan(`Last Updated: ${metrics.lastUpdated.toLocaleString()}`));
        }
    }
    catch (error) {
        errorHandler.handleError(error, 'Context management');
    }
});
// Tool statistics command
program
    .command('tools')
    .description('Show tool usage statistics and information')
    .option('--stats', 'Show usage statistics')
    .option('--list', 'List available tools')
    .action(async (options) => {
    try {
        await agentOrchestrator.initialize(process.cwd());
        const toolRegistry = agentOrchestrator.toolRegistry;
        if (options.stats) {
            const stats = toolRegistry.getToolUsageStats();
            console.log(chalk_1.default.blue.bold('\n🔧 Tool Usage Statistics:'));
            Object.entries(stats).forEach(([toolName, stat]) => {
                const successRate = stat.calls > 0 ? ((stat.successes / stat.calls) * 100).toFixed(1) : '0';
                console.log(chalk_1.default.cyan(`\n${toolName}:`));
                console.log(chalk_1.default.gray(`  Calls: ${stat.calls}`));
                console.log(chalk_1.default.gray(`  Success Rate: ${successRate}%`));
                console.log(chalk_1.default.gray(`  Average Duration: ${stat.averageDuration.toFixed(0)}ms`));
            });
            return;
        }
        if (options.list) {
            const tools = toolRegistry.getAllTools();
            console.log(chalk_1.default.blue.bold('\n🛠️  Available Tools:'));
            tools.forEach((tool) => {
                console.log(chalk_1.default.cyan(`\n${tool.name}:`));
                console.log(chalk_1.default.gray(`  Description: ${tool.description}`));
                console.log(chalk_1.default.gray(`  Parameters: ${Object.keys(tool.parameters.properties || {}).join(', ')}`));
            });
            return;
        }
        // Default: show both
        const tools = toolRegistry.getAllTools();
        const stats = toolRegistry.getToolUsageStats();
        console.log(chalk_1.default.blue.bold('\n🛠️  Tool Overview:'));
        console.log(chalk_1.default.cyan(`Total Tools: ${tools.length}`));
        const totalCalls = Object.values(stats).reduce((sum, stat) => sum + stat.calls, 0);
        console.log(chalk_1.default.cyan(`Total Executions: ${totalCalls}`));
    }
    catch (error) {
        errorHandler.handleError(error, 'Tool statistics');
    }
});
async function startChatLoop(context) {
    while (true) {
        try {
            const { input } = await inquirer_1.default.prompt({
                type: 'input',
                name: 'input',
                message: chalk_1.default.green('You:')
            });
            if (!input.trim())
                continue;
            // Handle special commands
            if (input.toLowerCase() === 'exit' || input.toLowerCase() === 'quit') {
                console.log(chalk_1.default.yellow('👋 Goodbye!'));
                break;
            }
            if (input.toLowerCase() === 'help') {
                showHelpMessage();
                continue;
            }
            if (input.toLowerCase() === 'clear') {
                await agentOrchestrator.clearConversation();
                console.log(chalk_1.default.yellow('🧹 Conversation cleared'));
                continue;
            }
            if (input.toLowerCase() === 'refresh') {
                await agentOrchestrator.refreshProjectContext();
                console.log(chalk_1.default.yellow('🔄 Project context refreshed'));
                continue;
            }
            if (input.toLowerCase().startsWith('autonomous:')) {
                const task = input.substring(11).trim();
                if (task) {
                    const spinner = (0, ora_1.default)('Executing autonomous task...').start();
                    try {
                        const result = await agentOrchestrator.executeAutonomousTask(task, context.workingDirectory, {
                            maxIterations: 5,
                            enableLearning: true,
                            saveProgress: true
                        });
                        spinner.succeed(`Autonomous task completed in ${result.iterations} iterations`);
                        console.log(chalk_1.default.blue('\nAI (Autonomous):'), result.finalResponse);
                        console.log('');
                    }
                    catch (error) {
                        spinner.fail('Autonomous task failed');
                        errorHandler.handleError(error, 'Autonomous task');
                    }
                    continue;
                }
            }
            // Process user input
            const spinner = (0, ora_1.default)('AI is thinking...').start();
            try {
                const response = await agentOrchestrator.processUserInput(input, context, {
                    enableToolCalling: true,
                    maxIterations: 5
                });
                spinner.stop();
                console.log(chalk_1.default.blue('\nAI:'), response);
                console.log('');
            }
            catch (error) {
                spinner.fail('AI processing failed');
                errorHandler.handleError(error, 'AI processing');
            }
        }
        catch (error) {
            if (error.name === 'ExitPromptError') {
                console.log(chalk_1.default.yellow('\n👋 Goodbye!'));
                break;
            }
            errorHandler.handleError(error, 'Chat loop');
        }
    }
    // Cleanup
    agentOrchestrator.cleanup();
}
function showHelpMessage() {
    console.log(chalk_1.default.blue.bold('\n🆘 Available Commands:'));
    console.log(chalk_1.default.cyan('help                    - Show this help message'));
    console.log(chalk_1.default.cyan('clear                   - Clear conversation history'));
    console.log(chalk_1.default.cyan('refresh                 - Refresh project context'));
    console.log(chalk_1.default.cyan('autonomous: <task>      - Execute autonomous task'));
    console.log(chalk_1.default.cyan('exit                    - Exit the chat session'));
    console.log(chalk_1.default.gray('\nOr just type your question/request naturally!'));
    console.log(chalk_1.default.blue.bold('\n🤖 Autonomous Mode:'));
    console.log(chalk_1.default.gray('Use "autonomous: <task description>" for complex multi-step tasks'));
    console.log(chalk_1.default.gray('Example: autonomous: analyze this codebase and suggest improvements'));
    console.log(chalk_1.default.blue.bold('\n🚀 Advanced Commands:'));
    console.log(chalk_1.default.cyan('task <description>      - Execute autonomous task with options'));
    console.log(chalk_1.default.cyan('advanced-task <desc>    - Execute with enhanced AI capabilities'));
    console.log(chalk_1.default.cyan('workflow <name>         - Execute predefined workflow'));
    console.log(chalk_1.default.cyan('analyze [directory]     - Analyze project health'));
    console.log(chalk_1.default.cyan('context                 - Manage project context'));
    console.log(chalk_1.default.cyan('tools                   - Show tool information'));
    console.log(chalk_1.default.cyan('status                  - Show system status'));
    console.log('');
}
async function interactiveConfig() {
    console.log(chalk_1.default.blue.bold('\n🔧 Interactive Configuration'));
    const { provider } = await inquirer_1.default.prompt([
        {
            type: 'list',
            name: 'provider',
            message: 'Select LLM provider:',
            choices: ['openai', 'anthropic', 'deepseek', 'ollama', 'gemini', 'mistral']
        }
    ]);
    configManager.updateAgentConfig({ provider });
    if (provider !== 'ollama') {
        const { apiKey } = await inquirer_1.default.prompt([
            {
                type: 'password',
                name: 'apiKey',
                message: `Enter API key for ${provider}:`,
                mask: '*'
            }
        ]);
        configManager.setProviderApiKey(provider, apiKey);
    }
    console.log(chalk_1.default.green('\n✅ Configuration saved successfully!'));
}
// Parse command line arguments
program.parse();
// If no command provided, show help
if (!process.argv.slice(2).length) {
    program.outputHelp();
}
//# sourceMappingURL=cli.js.map